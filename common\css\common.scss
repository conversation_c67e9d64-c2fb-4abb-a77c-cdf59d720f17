.occ-box {
  width: 100%;
  height: 120rpx;
}

.flex1 {
  flex: 1;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-start {
  display: flex;
  justify-content: flex-start;
}

.justify-center {
  display: flex;
  justify-content: center;
}

.justify-end {
  display: flex;
  justify-content: flex-end;
}

.justify-evenly {
  display: flex;
  justify-content: space-evenly;
}

.justify-around {
  display: flex;
  justify-content: space-around;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.align-start {
  display: flex;
  align-items: flex-start;
}

.align-center {
  display: flex;
  align-items: center;
}

.align-end {
  display: flex;
  align-items: flex-end;
}

.flex-wrap {
  display: flex;
  flex-wrap: wrap;
}

.pink {
  background-color: pink;
}

.pink1 {
  background-color: skyblue;
}

.pink2 {
  background-color: yellow;
}

.pink3 {
  background-color: red;
}

.pink4 {
  background-color: #0db0d6;
}

.pink5 {
  background-color: blue;
}

.bgfff {
  background-color: #fff;
}

.wh100 {
  width: 100%;
  height: 100%;
}

.top32 {
  margin-top: 32rpx;
}

.top22 {
  margin-top: 22rpx;
}

.top12 {
  margin-top: 12rpx;
}

.left12 {
  margin-left: 12rpx;
}

.left32 {
  margin-left: 32rpx;
}

.left22 {
  margin-left: 32rpx;
}

.radius32 {
  border-radius: 32rpx;
}

.padding220 {
  padding: 22rpx 0;
}

.padding32 {
  padding: 32rpx;
}

.padding22 {
  padding: 22rpx;
}

.padding032 {
  padding: 0 32rpx;
}

.padding320 {
  padding: 32rpx 0;
}

.padding012 {
  padding: 0 12rpx;
}

.padding120 {
  padding: 12rpx 0;
}

.padding12 {
  padding: 12rpx;
}

.white_color {
  color: #fff;
}

.absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}