<template>
  <view
    :style="{
      width: width,
      height: height,
      marginLeft: left,
      marginRight: right,
      marginTop: top,
      marginBottom: bottom,
      fontSize: size,
      fontWeight: weight,
      color: color,
      backgroundColor: backgroundColor,
    }"
    @click="handleClick"
    :class="myClass">
    {{ text }}
  </view>
</template>

<script>
export default {
  name: "my-text",
  props: {
    myClass: {
      type: String,
      default: "",
    },
    backgroundColor: {
      type: String,
      default: "",
    },
    width: {
      type: [Number, String],
      default: "",
    },
    height: {
      type: [Number, String],
      default: "",
    },
    color: {
      type: String,
      default: "#000",
    },
    top: {
      type: [Number, String],
      default: "",
    },
    right: {
      type: [Number, String],
      default: "",
    },
    bottom: {
      type: [Number, String],
      default: "",
    },
    left: {
      type: [Number, String],
      default: "",
    },
    text: {
      type: [Number, String],
      default: "",
    },
    size: {
      type: [Number, String],
      default: "26rpx",
    },
    weight: {
      type: Number,
      default: 400,
    },
  },
  methods: {
    handleClick() {
      this.$emit("click");
    },
  },
};
</script>

<style>
.my-Img {
  width: 20px;
  height: 20px;
  border-radius: ;
}
</style>
