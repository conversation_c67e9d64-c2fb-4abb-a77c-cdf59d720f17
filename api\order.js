import request, {
	Method
} from "@/utils/request";

/**
 * 获取订单
 * @returns {AxiosPromise}
 */
export function getProductIMG(params) {
	return request({
		url: "/v2/product/pictures/info",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}

/**
 * 根据标识符获取商品信息
 * @returns {AxiosPromise}
 */
export function getProductInfo(params) {
	return request({
		url: "/v3/product/info/list",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}


//url: "/v2/conditional-cancellation/list",
export function getCancellationOrder(params) {
	return request({
		url: "/api/posting-service/seller-ui/fbs/posting/list",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}


/**
 * 退货申请列表
 * @returns {AxiosPromise}
 */
export function getReturnList(params) {
	return request({
		url: "/v2/returns/rfbs/list",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}

/**
 * FBO和FBS退货信息
 * @returns {AxiosPromise}
 */
export function getFBSReturnList(params) {
	return request({
		url: "/v1/returns/list",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}

/**
 * 仓库清单
 * @returns {AxiosPromise}
 */
export function getWarehouseList(params) {
	return request({
		url: "/v1/warehouse/list",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}