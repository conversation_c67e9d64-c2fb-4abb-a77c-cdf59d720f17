"use strict";

exports.__esModule = true;
exports.arrayExpression = exports.ArrayExpression = ArrayExpression;
exports.assignmentExpression = exports.AssignmentExpression = AssignmentExpression;
exports.binaryExpression = exports.BinaryExpression = BinaryExpression;
exports.directive = exports.Directive = Directive;
exports.directiveLiteral = exports.DirectiveLiteral = DirectiveLiteral;
exports.blockStatement = exports.BlockStatement = BlockStatement;
exports.breakStatement = exports.BreakStatement = BreakStatement;
exports.callExpression = exports.CallExpression = CallExpression;
exports.catchClause = exports.CatchClause = CatchClause;
exports.conditionalExpression = exports.ConditionalExpression = ConditionalExpression;
exports.continueStatement = exports.ContinueStatement = ContinueStatement;
exports.debuggerStatement = exports.DebuggerStatement = DebuggerStatement;
exports.doWhileStatement = exports.DoWhileStatement = DoWhileStatement;
exports.emptyStatement = exports.EmptyStatement = EmptyStatement;
exports.expressionStatement = exports.ExpressionStatement = ExpressionStatement;
exports.file = exports.File = File;
exports.forInStatement = exports.ForInStatement = ForInStatement;
exports.forStatement = exports.ForStatement = ForStatement;
exports.functionDeclaration = exports.FunctionDeclaration = FunctionDeclaration;
exports.functionExpression = exports.FunctionExpression = FunctionExpression;
exports.identifier = exports.Identifier = Identifier;
exports.ifStatement = exports.IfStatement = IfStatement;
exports.labeledStatement = exports.LabeledStatement = LabeledStatement;
exports.stringLiteral = exports.StringLiteral = StringLiteral;
exports.numericLiteral = exports.NumericLiteral = NumericLiteral;
exports.nullLiteral = exports.NullLiteral = NullLiteral;
exports.booleanLiteral = exports.BooleanLiteral = BooleanLiteral;
exports.regExpLiteral = exports.RegExpLiteral = RegExpLiteral;
exports.logicalExpression = exports.LogicalExpression = LogicalExpression;
exports.memberExpression = exports.MemberExpression = MemberExpression;
exports.newExpression = exports.NewExpression = NewExpression;
exports.program = exports.Program = Program;
exports.objectExpression = exports.ObjectExpression = ObjectExpression;
exports.objectMethod = exports.ObjectMethod = ObjectMethod;
exports.objectProperty = exports.ObjectProperty = ObjectProperty;
exports.restElement = exports.RestElement = RestElement;
exports.returnStatement = exports.ReturnStatement = ReturnStatement;
exports.sequenceExpression = exports.SequenceExpression = SequenceExpression;
exports.switchCase = exports.SwitchCase = SwitchCase;
exports.switchStatement = exports.SwitchStatement = SwitchStatement;
exports.thisExpression = exports.ThisExpression = ThisExpression;
exports.throwStatement = exports.ThrowStatement = ThrowStatement;
exports.tryStatement = exports.TryStatement = TryStatement;
exports.unaryExpression = exports.UnaryExpression = UnaryExpression;
exports.updateExpression = exports.UpdateExpression = UpdateExpression;
exports.variableDeclaration = exports.VariableDeclaration = VariableDeclaration;
exports.variableDeclarator = exports.VariableDeclarator = VariableDeclarator;
exports.whileStatement = exports.WhileStatement = WhileStatement;
exports.withStatement = exports.WithStatement = WithStatement;
exports.assignmentPattern = exports.AssignmentPattern = AssignmentPattern;
exports.arrayPattern = exports.ArrayPattern = ArrayPattern;
exports.arrowFunctionExpression = exports.ArrowFunctionExpression = ArrowFunctionExpression;
exports.classBody = exports.ClassBody = ClassBody;
exports.classDeclaration = exports.ClassDeclaration = ClassDeclaration;
exports.classExpression = exports.ClassExpression = ClassExpression;
exports.exportAllDeclaration = exports.ExportAllDeclaration = ExportAllDeclaration;
exports.exportDefaultDeclaration = exports.ExportDefaultDeclaration = ExportDefaultDeclaration;
exports.exportNamedDeclaration = exports.ExportNamedDeclaration = ExportNamedDeclaration;
exports.exportSpecifier = exports.ExportSpecifier = ExportSpecifier;
exports.forOfStatement = exports.ForOfStatement = ForOfStatement;
exports.importDeclaration = exports.ImportDeclaration = ImportDeclaration;
exports.importDefaultSpecifier = exports.ImportDefaultSpecifier = ImportDefaultSpecifier;
exports.importNamespaceSpecifier = exports.ImportNamespaceSpecifier = ImportNamespaceSpecifier;
exports.importSpecifier = exports.ImportSpecifier = ImportSpecifier;
exports.metaProperty = exports.MetaProperty = MetaProperty;
exports.classMethod = exports.ClassMethod = ClassMethod;
exports.objectPattern = exports.ObjectPattern = ObjectPattern;
exports.spreadElement = exports.SpreadElement = SpreadElement;
exports.super = exports.Super = Super;
exports.taggedTemplateExpression = exports.TaggedTemplateExpression = TaggedTemplateExpression;
exports.templateElement = exports.TemplateElement = TemplateElement;
exports.templateLiteral = exports.TemplateLiteral = TemplateLiteral;
exports.yieldExpression = exports.YieldExpression = YieldExpression;
exports.anyTypeAnnotation = exports.AnyTypeAnnotation = AnyTypeAnnotation;
exports.arrayTypeAnnotation = exports.ArrayTypeAnnotation = ArrayTypeAnnotation;
exports.booleanTypeAnnotation = exports.BooleanTypeAnnotation = BooleanTypeAnnotation;
exports.booleanLiteralTypeAnnotation = exports.BooleanLiteralTypeAnnotation = BooleanLiteralTypeAnnotation;
exports.nullLiteralTypeAnnotation = exports.NullLiteralTypeAnnotation = NullLiteralTypeAnnotation;
exports.classImplements = exports.ClassImplements = ClassImplements;
exports.declareClass = exports.DeclareClass = DeclareClass;
exports.declareFunction = exports.DeclareFunction = DeclareFunction;
exports.declareInterface = exports.DeclareInterface = DeclareInterface;
exports.declareModule = exports.DeclareModule = DeclareModule;
exports.declareModuleExports = exports.DeclareModuleExports = DeclareModuleExports;
exports.declareTypeAlias = exports.DeclareTypeAlias = DeclareTypeAlias;
exports.declareOpaqueType = exports.DeclareOpaqueType = DeclareOpaqueType;
exports.declareVariable = exports.DeclareVariable = DeclareVariable;
exports.declareExportDeclaration = exports.DeclareExportDeclaration = DeclareExportDeclaration;
exports.declareExportAllDeclaration = exports.DeclareExportAllDeclaration = DeclareExportAllDeclaration;
exports.declaredPredicate = exports.DeclaredPredicate = DeclaredPredicate;
exports.existsTypeAnnotation = exports.ExistsTypeAnnotation = ExistsTypeAnnotation;
exports.functionTypeAnnotation = exports.FunctionTypeAnnotation = FunctionTypeAnnotation;
exports.functionTypeParam = exports.FunctionTypeParam = FunctionTypeParam;
exports.genericTypeAnnotation = exports.GenericTypeAnnotation = GenericTypeAnnotation;
exports.inferredPredicate = exports.InferredPredicate = InferredPredicate;
exports.interfaceExtends = exports.InterfaceExtends = InterfaceExtends;
exports.interfaceDeclaration = exports.InterfaceDeclaration = InterfaceDeclaration;
exports.intersectionTypeAnnotation = exports.IntersectionTypeAnnotation = IntersectionTypeAnnotation;
exports.mixedTypeAnnotation = exports.MixedTypeAnnotation = MixedTypeAnnotation;
exports.emptyTypeAnnotation = exports.EmptyTypeAnnotation = EmptyTypeAnnotation;
exports.nullableTypeAnnotation = exports.NullableTypeAnnotation = NullableTypeAnnotation;
exports.numberLiteralTypeAnnotation = exports.NumberLiteralTypeAnnotation = NumberLiteralTypeAnnotation;
exports.numberTypeAnnotation = exports.NumberTypeAnnotation = NumberTypeAnnotation;
exports.objectTypeAnnotation = exports.ObjectTypeAnnotation = ObjectTypeAnnotation;
exports.objectTypeCallProperty = exports.ObjectTypeCallProperty = ObjectTypeCallProperty;
exports.objectTypeIndexer = exports.ObjectTypeIndexer = ObjectTypeIndexer;
exports.objectTypeProperty = exports.ObjectTypeProperty = ObjectTypeProperty;
exports.objectTypeSpreadProperty = exports.ObjectTypeSpreadProperty = ObjectTypeSpreadProperty;
exports.opaqueType = exports.OpaqueType = OpaqueType;
exports.qualifiedTypeIdentifier = exports.QualifiedTypeIdentifier = QualifiedTypeIdentifier;
exports.stringLiteralTypeAnnotation = exports.StringLiteralTypeAnnotation = StringLiteralTypeAnnotation;
exports.stringTypeAnnotation = exports.StringTypeAnnotation = StringTypeAnnotation;
exports.thisTypeAnnotation = exports.ThisTypeAnnotation = ThisTypeAnnotation;
exports.tupleTypeAnnotation = exports.TupleTypeAnnotation = TupleTypeAnnotation;
exports.typeofTypeAnnotation = exports.TypeofTypeAnnotation = TypeofTypeAnnotation;
exports.typeAlias = exports.TypeAlias = TypeAlias;
exports.typeAnnotation = exports.TypeAnnotation = TypeAnnotation;
exports.typeCastExpression = exports.TypeCastExpression = TypeCastExpression;
exports.typeParameter = exports.TypeParameter = TypeParameter;
exports.typeParameterDeclaration = exports.TypeParameterDeclaration = TypeParameterDeclaration;
exports.typeParameterInstantiation = exports.TypeParameterInstantiation = TypeParameterInstantiation;
exports.unionTypeAnnotation = exports.UnionTypeAnnotation = UnionTypeAnnotation;
exports.voidTypeAnnotation = exports.VoidTypeAnnotation = VoidTypeAnnotation;
exports.jSXAttribute = exports.jsxAttribute = exports.JSXAttribute = JSXAttribute;
exports.jSXClosingElement = exports.jsxClosingElement = exports.JSXClosingElement = JSXClosingElement;
exports.jSXElement = exports.jsxElement = exports.JSXElement = JSXElement;
exports.jSXEmptyExpression = exports.jsxEmptyExpression = exports.JSXEmptyExpression = JSXEmptyExpression;
exports.jSXExpressionContainer = exports.jsxExpressionContainer = exports.JSXExpressionContainer = JSXExpressionContainer;
exports.jSXSpreadChild = exports.jsxSpreadChild = exports.JSXSpreadChild = JSXSpreadChild;
exports.jSXIdentifier = exports.jsxIdentifier = exports.JSXIdentifier = JSXIdentifier;
exports.jSXMemberExpression = exports.jsxMemberExpression = exports.JSXMemberExpression = JSXMemberExpression;
exports.jSXNamespacedName = exports.jsxNamespacedName = exports.JSXNamespacedName = JSXNamespacedName;
exports.jSXOpeningElement = exports.jsxOpeningElement = exports.JSXOpeningElement = JSXOpeningElement;
exports.jSXSpreadAttribute = exports.jsxSpreadAttribute = exports.JSXSpreadAttribute = JSXSpreadAttribute;
exports.jSXText = exports.jsxText = exports.JSXText = JSXText;
exports.jSXFragment = exports.jsxFragment = exports.JSXFragment = JSXFragment;
exports.jSXOpeningFragment = exports.jsxOpeningFragment = exports.JSXOpeningFragment = JSXOpeningFragment;
exports.jSXClosingFragment = exports.jsxClosingFragment = exports.JSXClosingFragment = JSXClosingFragment;
exports.noop = exports.Noop = Noop;
exports.parenthesizedExpression = exports.ParenthesizedExpression = ParenthesizedExpression;
exports.awaitExpression = exports.AwaitExpression = AwaitExpression;
exports.bindExpression = exports.BindExpression = BindExpression;
exports.classProperty = exports.ClassProperty = ClassProperty;
exports.import = exports.Import = Import;
exports.decorator = exports.Decorator = Decorator;
exports.doExpression = exports.DoExpression = DoExpression;
exports.exportDefaultSpecifier = exports.ExportDefaultSpecifier = ExportDefaultSpecifier;
exports.exportNamespaceSpecifier = exports.ExportNamespaceSpecifier = ExportNamespaceSpecifier;
exports.tSParameterProperty = exports.tsParameterProperty = exports.TSParameterProperty = TSParameterProperty;
exports.tSDeclareFunction = exports.tsDeclareFunction = exports.TSDeclareFunction = TSDeclareFunction;
exports.tSDeclareMethod = exports.tsDeclareMethod = exports.TSDeclareMethod = TSDeclareMethod;
exports.tSQualifiedName = exports.tsQualifiedName = exports.TSQualifiedName = TSQualifiedName;
exports.tSCallSignatureDeclaration = exports.tsCallSignatureDeclaration = exports.TSCallSignatureDeclaration = TSCallSignatureDeclaration;
exports.tSConstructSignatureDeclaration = exports.tsConstructSignatureDeclaration = exports.TSConstructSignatureDeclaration = TSConstructSignatureDeclaration;
exports.tSPropertySignature = exports.tsPropertySignature = exports.TSPropertySignature = TSPropertySignature;
exports.tSMethodSignature = exports.tsMethodSignature = exports.TSMethodSignature = TSMethodSignature;
exports.tSIndexSignature = exports.tsIndexSignature = exports.TSIndexSignature = TSIndexSignature;
exports.tSAnyKeyword = exports.tsAnyKeyword = exports.TSAnyKeyword = TSAnyKeyword;
exports.tSNumberKeyword = exports.tsNumberKeyword = exports.TSNumberKeyword = TSNumberKeyword;
exports.tSObjectKeyword = exports.tsObjectKeyword = exports.TSObjectKeyword = TSObjectKeyword;
exports.tSBooleanKeyword = exports.tsBooleanKeyword = exports.TSBooleanKeyword = TSBooleanKeyword;
exports.tSStringKeyword = exports.tsStringKeyword = exports.TSStringKeyword = TSStringKeyword;
exports.tSSymbolKeyword = exports.tsSymbolKeyword = exports.TSSymbolKeyword = TSSymbolKeyword;
exports.tSVoidKeyword = exports.tsVoidKeyword = exports.TSVoidKeyword = TSVoidKeyword;
exports.tSUndefinedKeyword = exports.tsUndefinedKeyword = exports.TSUndefinedKeyword = TSUndefinedKeyword;
exports.tSNullKeyword = exports.tsNullKeyword = exports.TSNullKeyword = TSNullKeyword;
exports.tSNeverKeyword = exports.tsNeverKeyword = exports.TSNeverKeyword = TSNeverKeyword;
exports.tSThisType = exports.tsThisType = exports.TSThisType = TSThisType;
exports.tSFunctionType = exports.tsFunctionType = exports.TSFunctionType = TSFunctionType;
exports.tSConstructorType = exports.tsConstructorType = exports.TSConstructorType = TSConstructorType;
exports.tSTypeReference = exports.tsTypeReference = exports.TSTypeReference = TSTypeReference;
exports.tSTypePredicate = exports.tsTypePredicate = exports.TSTypePredicate = TSTypePredicate;
exports.tSTypeQuery = exports.tsTypeQuery = exports.TSTypeQuery = TSTypeQuery;
exports.tSTypeLiteral = exports.tsTypeLiteral = exports.TSTypeLiteral = TSTypeLiteral;
exports.tSArrayType = exports.tsArrayType = exports.TSArrayType = TSArrayType;
exports.tSTupleType = exports.tsTupleType = exports.TSTupleType = TSTupleType;
exports.tSUnionType = exports.tsUnionType = exports.TSUnionType = TSUnionType;
exports.tSIntersectionType = exports.tsIntersectionType = exports.TSIntersectionType = TSIntersectionType;
exports.tSParenthesizedType = exports.tsParenthesizedType = exports.TSParenthesizedType = TSParenthesizedType;
exports.tSTypeOperator = exports.tsTypeOperator = exports.TSTypeOperator = TSTypeOperator;
exports.tSIndexedAccessType = exports.tsIndexedAccessType = exports.TSIndexedAccessType = TSIndexedAccessType;
exports.tSMappedType = exports.tsMappedType = exports.TSMappedType = TSMappedType;
exports.tSLiteralType = exports.tsLiteralType = exports.TSLiteralType = TSLiteralType;
exports.tSExpressionWithTypeArguments = exports.tsExpressionWithTypeArguments = exports.TSExpressionWithTypeArguments = TSExpressionWithTypeArguments;
exports.tSInterfaceDeclaration = exports.tsInterfaceDeclaration = exports.TSInterfaceDeclaration = TSInterfaceDeclaration;
exports.tSInterfaceBody = exports.tsInterfaceBody = exports.TSInterfaceBody = TSInterfaceBody;
exports.tSTypeAliasDeclaration = exports.tsTypeAliasDeclaration = exports.TSTypeAliasDeclaration = TSTypeAliasDeclaration;
exports.tSAsExpression = exports.tsAsExpression = exports.TSAsExpression = TSAsExpression;
exports.tSTypeAssertion = exports.tsTypeAssertion = exports.TSTypeAssertion = TSTypeAssertion;
exports.tSEnumDeclaration = exports.tsEnumDeclaration = exports.TSEnumDeclaration = TSEnumDeclaration;
exports.tSEnumMember = exports.tsEnumMember = exports.TSEnumMember = TSEnumMember;
exports.tSModuleDeclaration = exports.tsModuleDeclaration = exports.TSModuleDeclaration = TSModuleDeclaration;
exports.tSModuleBlock = exports.tsModuleBlock = exports.TSModuleBlock = TSModuleBlock;
exports.tSImportEqualsDeclaration = exports.tsImportEqualsDeclaration = exports.TSImportEqualsDeclaration = TSImportEqualsDeclaration;
exports.tSExternalModuleReference = exports.tsExternalModuleReference = exports.TSExternalModuleReference = TSExternalModuleReference;
exports.tSNonNullExpression = exports.tsNonNullExpression = exports.TSNonNullExpression = TSNonNullExpression;
exports.tSExportAssignment = exports.tsExportAssignment = exports.TSExportAssignment = TSExportAssignment;
exports.tSNamespaceExportDeclaration = exports.tsNamespaceExportDeclaration = exports.TSNamespaceExportDeclaration = TSNamespaceExportDeclaration;
exports.tSTypeAnnotation = exports.tsTypeAnnotation = exports.TSTypeAnnotation = TSTypeAnnotation;
exports.tSTypeParameterInstantiation = exports.tsTypeParameterInstantiation = exports.TSTypeParameterInstantiation = TSTypeParameterInstantiation;
exports.tSTypeParameterDeclaration = exports.tsTypeParameterDeclaration = exports.TSTypeParameterDeclaration = TSTypeParameterDeclaration;
exports.tSTypeParameter = exports.tsTypeParameter = exports.TSTypeParameter = TSTypeParameter;
exports.numberLiteral = exports.NumberLiteral = NumberLiteral;
exports.regexLiteral = exports.RegexLiteral = RegexLiteral;
exports.restProperty = exports.RestProperty = RestProperty;
exports.spreadProperty = exports.SpreadProperty = SpreadProperty;

var _builder = _interopRequireDefault(require("../builder"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function ArrayExpression() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }

  return _builder.default.apply(void 0, ["ArrayExpression"].concat(args));
}

function AssignmentExpression() {
  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
    args[_key2] = arguments[_key2];
  }

  return _builder.default.apply(void 0, ["AssignmentExpression"].concat(args));
}

function BinaryExpression() {
  for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
    args[_key3] = arguments[_key3];
  }

  return _builder.default.apply(void 0, ["BinaryExpression"].concat(args));
}

function Directive() {
  for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {
    args[_key4] = arguments[_key4];
  }

  return _builder.default.apply(void 0, ["Directive"].concat(args));
}

function DirectiveLiteral() {
  for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {
    args[_key5] = arguments[_key5];
  }

  return _builder.default.apply(void 0, ["DirectiveLiteral"].concat(args));
}

function BlockStatement() {
  for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {
    args[_key6] = arguments[_key6];
  }

  return _builder.default.apply(void 0, ["BlockStatement"].concat(args));
}

function BreakStatement() {
  for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {
    args[_key7] = arguments[_key7];
  }

  return _builder.default.apply(void 0, ["BreakStatement"].concat(args));
}

function CallExpression() {
  for (var _len8 = arguments.length, args = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {
    args[_key8] = arguments[_key8];
  }

  return _builder.default.apply(void 0, ["CallExpression"].concat(args));
}

function CatchClause() {
  for (var _len9 = arguments.length, args = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++) {
    args[_key9] = arguments[_key9];
  }

  return _builder.default.apply(void 0, ["CatchClause"].concat(args));
}

function ConditionalExpression() {
  for (var _len10 = arguments.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {
    args[_key10] = arguments[_key10];
  }

  return _builder.default.apply(void 0, ["ConditionalExpression"].concat(args));
}

function ContinueStatement() {
  for (var _len11 = arguments.length, args = new Array(_len11), _key11 = 0; _key11 < _len11; _key11++) {
    args[_key11] = arguments[_key11];
  }

  return _builder.default.apply(void 0, ["ContinueStatement"].concat(args));
}

function DebuggerStatement() {
  for (var _len12 = arguments.length, args = new Array(_len12), _key12 = 0; _key12 < _len12; _key12++) {
    args[_key12] = arguments[_key12];
  }

  return _builder.default.apply(void 0, ["DebuggerStatement"].concat(args));
}

function DoWhileStatement() {
  for (var _len13 = arguments.length, args = new Array(_len13), _key13 = 0; _key13 < _len13; _key13++) {
    args[_key13] = arguments[_key13];
  }

  return _builder.default.apply(void 0, ["DoWhileStatement"].concat(args));
}

function EmptyStatement() {
  for (var _len14 = arguments.length, args = new Array(_len14), _key14 = 0; _key14 < _len14; _key14++) {
    args[_key14] = arguments[_key14];
  }

  return _builder.default.apply(void 0, ["EmptyStatement"].concat(args));
}

function ExpressionStatement() {
  for (var _len15 = arguments.length, args = new Array(_len15), _key15 = 0; _key15 < _len15; _key15++) {
    args[_key15] = arguments[_key15];
  }

  return _builder.default.apply(void 0, ["ExpressionStatement"].concat(args));
}

function File() {
  for (var _len16 = arguments.length, args = new Array(_len16), _key16 = 0; _key16 < _len16; _key16++) {
    args[_key16] = arguments[_key16];
  }

  return _builder.default.apply(void 0, ["File"].concat(args));
}

function ForInStatement() {
  for (var _len17 = arguments.length, args = new Array(_len17), _key17 = 0; _key17 < _len17; _key17++) {
    args[_key17] = arguments[_key17];
  }

  return _builder.default.apply(void 0, ["ForInStatement"].concat(args));
}

function ForStatement() {
  for (var _len18 = arguments.length, args = new Array(_len18), _key18 = 0; _key18 < _len18; _key18++) {
    args[_key18] = arguments[_key18];
  }

  return _builder.default.apply(void 0, ["ForStatement"].concat(args));
}

function FunctionDeclaration() {
  for (var _len19 = arguments.length, args = new Array(_len19), _key19 = 0; _key19 < _len19; _key19++) {
    args[_key19] = arguments[_key19];
  }

  return _builder.default.apply(void 0, ["FunctionDeclaration"].concat(args));
}

function FunctionExpression() {
  for (var _len20 = arguments.length, args = new Array(_len20), _key20 = 0; _key20 < _len20; _key20++) {
    args[_key20] = arguments[_key20];
  }

  return _builder.default.apply(void 0, ["FunctionExpression"].concat(args));
}

function Identifier() {
  for (var _len21 = arguments.length, args = new Array(_len21), _key21 = 0; _key21 < _len21; _key21++) {
    args[_key21] = arguments[_key21];
  }

  return _builder.default.apply(void 0, ["Identifier"].concat(args));
}

function IfStatement() {
  for (var _len22 = arguments.length, args = new Array(_len22), _key22 = 0; _key22 < _len22; _key22++) {
    args[_key22] = arguments[_key22];
  }

  return _builder.default.apply(void 0, ["IfStatement"].concat(args));
}

function LabeledStatement() {
  for (var _len23 = arguments.length, args = new Array(_len23), _key23 = 0; _key23 < _len23; _key23++) {
    args[_key23] = arguments[_key23];
  }

  return _builder.default.apply(void 0, ["LabeledStatement"].concat(args));
}

function StringLiteral() {
  for (var _len24 = arguments.length, args = new Array(_len24), _key24 = 0; _key24 < _len24; _key24++) {
    args[_key24] = arguments[_key24];
  }

  return _builder.default.apply(void 0, ["StringLiteral"].concat(args));
}

function NumericLiteral() {
  for (var _len25 = arguments.length, args = new Array(_len25), _key25 = 0; _key25 < _len25; _key25++) {
    args[_key25] = arguments[_key25];
  }

  return _builder.default.apply(void 0, ["NumericLiteral"].concat(args));
}

function NullLiteral() {
  for (var _len26 = arguments.length, args = new Array(_len26), _key26 = 0; _key26 < _len26; _key26++) {
    args[_key26] = arguments[_key26];
  }

  return _builder.default.apply(void 0, ["NullLiteral"].concat(args));
}

function BooleanLiteral() {
  for (var _len27 = arguments.length, args = new Array(_len27), _key27 = 0; _key27 < _len27; _key27++) {
    args[_key27] = arguments[_key27];
  }

  return _builder.default.apply(void 0, ["BooleanLiteral"].concat(args));
}

function RegExpLiteral() {
  for (var _len28 = arguments.length, args = new Array(_len28), _key28 = 0; _key28 < _len28; _key28++) {
    args[_key28] = arguments[_key28];
  }

  return _builder.default.apply(void 0, ["RegExpLiteral"].concat(args));
}

function LogicalExpression() {
  for (var _len29 = arguments.length, args = new Array(_len29), _key29 = 0; _key29 < _len29; _key29++) {
    args[_key29] = arguments[_key29];
  }

  return _builder.default.apply(void 0, ["LogicalExpression"].concat(args));
}

function MemberExpression() {
  for (var _len30 = arguments.length, args = new Array(_len30), _key30 = 0; _key30 < _len30; _key30++) {
    args[_key30] = arguments[_key30];
  }

  return _builder.default.apply(void 0, ["MemberExpression"].concat(args));
}

function NewExpression() {
  for (var _len31 = arguments.length, args = new Array(_len31), _key31 = 0; _key31 < _len31; _key31++) {
    args[_key31] = arguments[_key31];
  }

  return _builder.default.apply(void 0, ["NewExpression"].concat(args));
}

function Program() {
  for (var _len32 = arguments.length, args = new Array(_len32), _key32 = 0; _key32 < _len32; _key32++) {
    args[_key32] = arguments[_key32];
  }

  return _builder.default.apply(void 0, ["Program"].concat(args));
}

function ObjectExpression() {
  for (var _len33 = arguments.length, args = new Array(_len33), _key33 = 0; _key33 < _len33; _key33++) {
    args[_key33] = arguments[_key33];
  }

  return _builder.default.apply(void 0, ["ObjectExpression"].concat(args));
}

function ObjectMethod() {
  for (var _len34 = arguments.length, args = new Array(_len34), _key34 = 0; _key34 < _len34; _key34++) {
    args[_key34] = arguments[_key34];
  }

  return _builder.default.apply(void 0, ["ObjectMethod"].concat(args));
}

function ObjectProperty() {
  for (var _len35 = arguments.length, args = new Array(_len35), _key35 = 0; _key35 < _len35; _key35++) {
    args[_key35] = arguments[_key35];
  }

  return _builder.default.apply(void 0, ["ObjectProperty"].concat(args));
}

function RestElement() {
  for (var _len36 = arguments.length, args = new Array(_len36), _key36 = 0; _key36 < _len36; _key36++) {
    args[_key36] = arguments[_key36];
  }

  return _builder.default.apply(void 0, ["RestElement"].concat(args));
}

function ReturnStatement() {
  for (var _len37 = arguments.length, args = new Array(_len37), _key37 = 0; _key37 < _len37; _key37++) {
    args[_key37] = arguments[_key37];
  }

  return _builder.default.apply(void 0, ["ReturnStatement"].concat(args));
}

function SequenceExpression() {
  for (var _len38 = arguments.length, args = new Array(_len38), _key38 = 0; _key38 < _len38; _key38++) {
    args[_key38] = arguments[_key38];
  }

  return _builder.default.apply(void 0, ["SequenceExpression"].concat(args));
}

function SwitchCase() {
  for (var _len39 = arguments.length, args = new Array(_len39), _key39 = 0; _key39 < _len39; _key39++) {
    args[_key39] = arguments[_key39];
  }

  return _builder.default.apply(void 0, ["SwitchCase"].concat(args));
}

function SwitchStatement() {
  for (var _len40 = arguments.length, args = new Array(_len40), _key40 = 0; _key40 < _len40; _key40++) {
    args[_key40] = arguments[_key40];
  }

  return _builder.default.apply(void 0, ["SwitchStatement"].concat(args));
}

function ThisExpression() {
  for (var _len41 = arguments.length, args = new Array(_len41), _key41 = 0; _key41 < _len41; _key41++) {
    args[_key41] = arguments[_key41];
  }

  return _builder.default.apply(void 0, ["ThisExpression"].concat(args));
}

function ThrowStatement() {
  for (var _len42 = arguments.length, args = new Array(_len42), _key42 = 0; _key42 < _len42; _key42++) {
    args[_key42] = arguments[_key42];
  }

  return _builder.default.apply(void 0, ["ThrowStatement"].concat(args));
}

function TryStatement() {
  for (var _len43 = arguments.length, args = new Array(_len43), _key43 = 0; _key43 < _len43; _key43++) {
    args[_key43] = arguments[_key43];
  }

  return _builder.default.apply(void 0, ["TryStatement"].concat(args));
}

function UnaryExpression() {
  for (var _len44 = arguments.length, args = new Array(_len44), _key44 = 0; _key44 < _len44; _key44++) {
    args[_key44] = arguments[_key44];
  }

  return _builder.default.apply(void 0, ["UnaryExpression"].concat(args));
}

function UpdateExpression() {
  for (var _len45 = arguments.length, args = new Array(_len45), _key45 = 0; _key45 < _len45; _key45++) {
    args[_key45] = arguments[_key45];
  }

  return _builder.default.apply(void 0, ["UpdateExpression"].concat(args));
}

function VariableDeclaration() {
  for (var _len46 = arguments.length, args = new Array(_len46), _key46 = 0; _key46 < _len46; _key46++) {
    args[_key46] = arguments[_key46];
  }

  return _builder.default.apply(void 0, ["VariableDeclaration"].concat(args));
}

function VariableDeclarator() {
  for (var _len47 = arguments.length, args = new Array(_len47), _key47 = 0; _key47 < _len47; _key47++) {
    args[_key47] = arguments[_key47];
  }

  return _builder.default.apply(void 0, ["VariableDeclarator"].concat(args));
}

function WhileStatement() {
  for (var _len48 = arguments.length, args = new Array(_len48), _key48 = 0; _key48 < _len48; _key48++) {
    args[_key48] = arguments[_key48];
  }

  return _builder.default.apply(void 0, ["WhileStatement"].concat(args));
}

function WithStatement() {
  for (var _len49 = arguments.length, args = new Array(_len49), _key49 = 0; _key49 < _len49; _key49++) {
    args[_key49] = arguments[_key49];
  }

  return _builder.default.apply(void 0, ["WithStatement"].concat(args));
}

function AssignmentPattern() {
  for (var _len50 = arguments.length, args = new Array(_len50), _key50 = 0; _key50 < _len50; _key50++) {
    args[_key50] = arguments[_key50];
  }

  return _builder.default.apply(void 0, ["AssignmentPattern"].concat(args));
}

function ArrayPattern() {
  for (var _len51 = arguments.length, args = new Array(_len51), _key51 = 0; _key51 < _len51; _key51++) {
    args[_key51] = arguments[_key51];
  }

  return _builder.default.apply(void 0, ["ArrayPattern"].concat(args));
}

function ArrowFunctionExpression() {
  for (var _len52 = arguments.length, args = new Array(_len52), _key52 = 0; _key52 < _len52; _key52++) {
    args[_key52] = arguments[_key52];
  }

  return _builder.default.apply(void 0, ["ArrowFunctionExpression"].concat(args));
}

function ClassBody() {
  for (var _len53 = arguments.length, args = new Array(_len53), _key53 = 0; _key53 < _len53; _key53++) {
    args[_key53] = arguments[_key53];
  }

  return _builder.default.apply(void 0, ["ClassBody"].concat(args));
}

function ClassDeclaration() {
  for (var _len54 = arguments.length, args = new Array(_len54), _key54 = 0; _key54 < _len54; _key54++) {
    args[_key54] = arguments[_key54];
  }

  return _builder.default.apply(void 0, ["ClassDeclaration"].concat(args));
}

function ClassExpression() {
  for (var _len55 = arguments.length, args = new Array(_len55), _key55 = 0; _key55 < _len55; _key55++) {
    args[_key55] = arguments[_key55];
  }

  return _builder.default.apply(void 0, ["ClassExpression"].concat(args));
}

function ExportAllDeclaration() {
  for (var _len56 = arguments.length, args = new Array(_len56), _key56 = 0; _key56 < _len56; _key56++) {
    args[_key56] = arguments[_key56];
  }

  return _builder.default.apply(void 0, ["ExportAllDeclaration"].concat(args));
}

function ExportDefaultDeclaration() {
  for (var _len57 = arguments.length, args = new Array(_len57), _key57 = 0; _key57 < _len57; _key57++) {
    args[_key57] = arguments[_key57];
  }

  return _builder.default.apply(void 0, ["ExportDefaultDeclaration"].concat(args));
}

function ExportNamedDeclaration() {
  for (var _len58 = arguments.length, args = new Array(_len58), _key58 = 0; _key58 < _len58; _key58++) {
    args[_key58] = arguments[_key58];
  }

  return _builder.default.apply(void 0, ["ExportNamedDeclaration"].concat(args));
}

function ExportSpecifier() {
  for (var _len59 = arguments.length, args = new Array(_len59), _key59 = 0; _key59 < _len59; _key59++) {
    args[_key59] = arguments[_key59];
  }

  return _builder.default.apply(void 0, ["ExportSpecifier"].concat(args));
}

function ForOfStatement() {
  for (var _len60 = arguments.length, args = new Array(_len60), _key60 = 0; _key60 < _len60; _key60++) {
    args[_key60] = arguments[_key60];
  }

  return _builder.default.apply(void 0, ["ForOfStatement"].concat(args));
}

function ImportDeclaration() {
  for (var _len61 = arguments.length, args = new Array(_len61), _key61 = 0; _key61 < _len61; _key61++) {
    args[_key61] = arguments[_key61];
  }

  return _builder.default.apply(void 0, ["ImportDeclaration"].concat(args));
}

function ImportDefaultSpecifier() {
  for (var _len62 = arguments.length, args = new Array(_len62), _key62 = 0; _key62 < _len62; _key62++) {
    args[_key62] = arguments[_key62];
  }

  return _builder.default.apply(void 0, ["ImportDefaultSpecifier"].concat(args));
}

function ImportNamespaceSpecifier() {
  for (var _len63 = arguments.length, args = new Array(_len63), _key63 = 0; _key63 < _len63; _key63++) {
    args[_key63] = arguments[_key63];
  }

  return _builder.default.apply(void 0, ["ImportNamespaceSpecifier"].concat(args));
}

function ImportSpecifier() {
  for (var _len64 = arguments.length, args = new Array(_len64), _key64 = 0; _key64 < _len64; _key64++) {
    args[_key64] = arguments[_key64];
  }

  return _builder.default.apply(void 0, ["ImportSpecifier"].concat(args));
}

function MetaProperty() {
  for (var _len65 = arguments.length, args = new Array(_len65), _key65 = 0; _key65 < _len65; _key65++) {
    args[_key65] = arguments[_key65];
  }

  return _builder.default.apply(void 0, ["MetaProperty"].concat(args));
}

function ClassMethod() {
  for (var _len66 = arguments.length, args = new Array(_len66), _key66 = 0; _key66 < _len66; _key66++) {
    args[_key66] = arguments[_key66];
  }

  return _builder.default.apply(void 0, ["ClassMethod"].concat(args));
}

function ObjectPattern() {
  for (var _len67 = arguments.length, args = new Array(_len67), _key67 = 0; _key67 < _len67; _key67++) {
    args[_key67] = arguments[_key67];
  }

  return _builder.default.apply(void 0, ["ObjectPattern"].concat(args));
}

function SpreadElement() {
  for (var _len68 = arguments.length, args = new Array(_len68), _key68 = 0; _key68 < _len68; _key68++) {
    args[_key68] = arguments[_key68];
  }

  return _builder.default.apply(void 0, ["SpreadElement"].concat(args));
}

function Super() {
  for (var _len69 = arguments.length, args = new Array(_len69), _key69 = 0; _key69 < _len69; _key69++) {
    args[_key69] = arguments[_key69];
  }

  return _builder.default.apply(void 0, ["Super"].concat(args));
}

function TaggedTemplateExpression() {
  for (var _len70 = arguments.length, args = new Array(_len70), _key70 = 0; _key70 < _len70; _key70++) {
    args[_key70] = arguments[_key70];
  }

  return _builder.default.apply(void 0, ["TaggedTemplateExpression"].concat(args));
}

function TemplateElement() {
  for (var _len71 = arguments.length, args = new Array(_len71), _key71 = 0; _key71 < _len71; _key71++) {
    args[_key71] = arguments[_key71];
  }

  return _builder.default.apply(void 0, ["TemplateElement"].concat(args));
}

function TemplateLiteral() {
  for (var _len72 = arguments.length, args = new Array(_len72), _key72 = 0; _key72 < _len72; _key72++) {
    args[_key72] = arguments[_key72];
  }

  return _builder.default.apply(void 0, ["TemplateLiteral"].concat(args));
}

function YieldExpression() {
  for (var _len73 = arguments.length, args = new Array(_len73), _key73 = 0; _key73 < _len73; _key73++) {
    args[_key73] = arguments[_key73];
  }

  return _builder.default.apply(void 0, ["YieldExpression"].concat(args));
}

function AnyTypeAnnotation() {
  for (var _len74 = arguments.length, args = new Array(_len74), _key74 = 0; _key74 < _len74; _key74++) {
    args[_key74] = arguments[_key74];
  }

  return _builder.default.apply(void 0, ["AnyTypeAnnotation"].concat(args));
}

function ArrayTypeAnnotation() {
  for (var _len75 = arguments.length, args = new Array(_len75), _key75 = 0; _key75 < _len75; _key75++) {
    args[_key75] = arguments[_key75];
  }

  return _builder.default.apply(void 0, ["ArrayTypeAnnotation"].concat(args));
}

function BooleanTypeAnnotation() {
  for (var _len76 = arguments.length, args = new Array(_len76), _key76 = 0; _key76 < _len76; _key76++) {
    args[_key76] = arguments[_key76];
  }

  return _builder.default.apply(void 0, ["BooleanTypeAnnotation"].concat(args));
}

function BooleanLiteralTypeAnnotation() {
  for (var _len77 = arguments.length, args = new Array(_len77), _key77 = 0; _key77 < _len77; _key77++) {
    args[_key77] = arguments[_key77];
  }

  return _builder.default.apply(void 0, ["BooleanLiteralTypeAnnotation"].concat(args));
}

function NullLiteralTypeAnnotation() {
  for (var _len78 = arguments.length, args = new Array(_len78), _key78 = 0; _key78 < _len78; _key78++) {
    args[_key78] = arguments[_key78];
  }

  return _builder.default.apply(void 0, ["NullLiteralTypeAnnotation"].concat(args));
}

function ClassImplements() {
  for (var _len79 = arguments.length, args = new Array(_len79), _key79 = 0; _key79 < _len79; _key79++) {
    args[_key79] = arguments[_key79];
  }

  return _builder.default.apply(void 0, ["ClassImplements"].concat(args));
}

function DeclareClass() {
  for (var _len80 = arguments.length, args = new Array(_len80), _key80 = 0; _key80 < _len80; _key80++) {
    args[_key80] = arguments[_key80];
  }

  return _builder.default.apply(void 0, ["DeclareClass"].concat(args));
}

function DeclareFunction() {
  for (var _len81 = arguments.length, args = new Array(_len81), _key81 = 0; _key81 < _len81; _key81++) {
    args[_key81] = arguments[_key81];
  }

  return _builder.default.apply(void 0, ["DeclareFunction"].concat(args));
}

function DeclareInterface() {
  for (var _len82 = arguments.length, args = new Array(_len82), _key82 = 0; _key82 < _len82; _key82++) {
    args[_key82] = arguments[_key82];
  }

  return _builder.default.apply(void 0, ["DeclareInterface"].concat(args));
}

function DeclareModule() {
  for (var _len83 = arguments.length, args = new Array(_len83), _key83 = 0; _key83 < _len83; _key83++) {
    args[_key83] = arguments[_key83];
  }

  return _builder.default.apply(void 0, ["DeclareModule"].concat(args));
}

function DeclareModuleExports() {
  for (var _len84 = arguments.length, args = new Array(_len84), _key84 = 0; _key84 < _len84; _key84++) {
    args[_key84] = arguments[_key84];
  }

  return _builder.default.apply(void 0, ["DeclareModuleExports"].concat(args));
}

function DeclareTypeAlias() {
  for (var _len85 = arguments.length, args = new Array(_len85), _key85 = 0; _key85 < _len85; _key85++) {
    args[_key85] = arguments[_key85];
  }

  return _builder.default.apply(void 0, ["DeclareTypeAlias"].concat(args));
}

function DeclareOpaqueType() {
  for (var _len86 = arguments.length, args = new Array(_len86), _key86 = 0; _key86 < _len86; _key86++) {
    args[_key86] = arguments[_key86];
  }

  return _builder.default.apply(void 0, ["DeclareOpaqueType"].concat(args));
}

function DeclareVariable() {
  for (var _len87 = arguments.length, args = new Array(_len87), _key87 = 0; _key87 < _len87; _key87++) {
    args[_key87] = arguments[_key87];
  }

  return _builder.default.apply(void 0, ["DeclareVariable"].concat(args));
}

function DeclareExportDeclaration() {
  for (var _len88 = arguments.length, args = new Array(_len88), _key88 = 0; _key88 < _len88; _key88++) {
    args[_key88] = arguments[_key88];
  }

  return _builder.default.apply(void 0, ["DeclareExportDeclaration"].concat(args));
}

function DeclareExportAllDeclaration() {
  for (var _len89 = arguments.length, args = new Array(_len89), _key89 = 0; _key89 < _len89; _key89++) {
    args[_key89] = arguments[_key89];
  }

  return _builder.default.apply(void 0, ["DeclareExportAllDeclaration"].concat(args));
}

function DeclaredPredicate() {
  for (var _len90 = arguments.length, args = new Array(_len90), _key90 = 0; _key90 < _len90; _key90++) {
    args[_key90] = arguments[_key90];
  }

  return _builder.default.apply(void 0, ["DeclaredPredicate"].concat(args));
}

function ExistsTypeAnnotation() {
  for (var _len91 = arguments.length, args = new Array(_len91), _key91 = 0; _key91 < _len91; _key91++) {
    args[_key91] = arguments[_key91];
  }

  return _builder.default.apply(void 0, ["ExistsTypeAnnotation"].concat(args));
}

function FunctionTypeAnnotation() {
  for (var _len92 = arguments.length, args = new Array(_len92), _key92 = 0; _key92 < _len92; _key92++) {
    args[_key92] = arguments[_key92];
  }

  return _builder.default.apply(void 0, ["FunctionTypeAnnotation"].concat(args));
}

function FunctionTypeParam() {
  for (var _len93 = arguments.length, args = new Array(_len93), _key93 = 0; _key93 < _len93; _key93++) {
    args[_key93] = arguments[_key93];
  }

  return _builder.default.apply(void 0, ["FunctionTypeParam"].concat(args));
}

function GenericTypeAnnotation() {
  for (var _len94 = arguments.length, args = new Array(_len94), _key94 = 0; _key94 < _len94; _key94++) {
    args[_key94] = arguments[_key94];
  }

  return _builder.default.apply(void 0, ["GenericTypeAnnotation"].concat(args));
}

function InferredPredicate() {
  for (var _len95 = arguments.length, args = new Array(_len95), _key95 = 0; _key95 < _len95; _key95++) {
    args[_key95] = arguments[_key95];
  }

  return _builder.default.apply(void 0, ["InferredPredicate"].concat(args));
}

function InterfaceExtends() {
  for (var _len96 = arguments.length, args = new Array(_len96), _key96 = 0; _key96 < _len96; _key96++) {
    args[_key96] = arguments[_key96];
  }

  return _builder.default.apply(void 0, ["InterfaceExtends"].concat(args));
}

function InterfaceDeclaration() {
  for (var _len97 = arguments.length, args = new Array(_len97), _key97 = 0; _key97 < _len97; _key97++) {
    args[_key97] = arguments[_key97];
  }

  return _builder.default.apply(void 0, ["InterfaceDeclaration"].concat(args));
}

function IntersectionTypeAnnotation() {
  for (var _len98 = arguments.length, args = new Array(_len98), _key98 = 0; _key98 < _len98; _key98++) {
    args[_key98] = arguments[_key98];
  }

  return _builder.default.apply(void 0, ["IntersectionTypeAnnotation"].concat(args));
}

function MixedTypeAnnotation() {
  for (var _len99 = arguments.length, args = new Array(_len99), _key99 = 0; _key99 < _len99; _key99++) {
    args[_key99] = arguments[_key99];
  }

  return _builder.default.apply(void 0, ["MixedTypeAnnotation"].concat(args));
}

function EmptyTypeAnnotation() {
  for (var _len100 = arguments.length, args = new Array(_len100), _key100 = 0; _key100 < _len100; _key100++) {
    args[_key100] = arguments[_key100];
  }

  return _builder.default.apply(void 0, ["EmptyTypeAnnotation"].concat(args));
}

function NullableTypeAnnotation() {
  for (var _len101 = arguments.length, args = new Array(_len101), _key101 = 0; _key101 < _len101; _key101++) {
    args[_key101] = arguments[_key101];
  }

  return _builder.default.apply(void 0, ["NullableTypeAnnotation"].concat(args));
}

function NumberLiteralTypeAnnotation() {
  for (var _len102 = arguments.length, args = new Array(_len102), _key102 = 0; _key102 < _len102; _key102++) {
    args[_key102] = arguments[_key102];
  }

  return _builder.default.apply(void 0, ["NumberLiteralTypeAnnotation"].concat(args));
}

function NumberTypeAnnotation() {
  for (var _len103 = arguments.length, args = new Array(_len103), _key103 = 0; _key103 < _len103; _key103++) {
    args[_key103] = arguments[_key103];
  }

  return _builder.default.apply(void 0, ["NumberTypeAnnotation"].concat(args));
}

function ObjectTypeAnnotation() {
  for (var _len104 = arguments.length, args = new Array(_len104), _key104 = 0; _key104 < _len104; _key104++) {
    args[_key104] = arguments[_key104];
  }

  return _builder.default.apply(void 0, ["ObjectTypeAnnotation"].concat(args));
}

function ObjectTypeCallProperty() {
  for (var _len105 = arguments.length, args = new Array(_len105), _key105 = 0; _key105 < _len105; _key105++) {
    args[_key105] = arguments[_key105];
  }

  return _builder.default.apply(void 0, ["ObjectTypeCallProperty"].concat(args));
}

function ObjectTypeIndexer() {
  for (var _len106 = arguments.length, args = new Array(_len106), _key106 = 0; _key106 < _len106; _key106++) {
    args[_key106] = arguments[_key106];
  }

  return _builder.default.apply(void 0, ["ObjectTypeIndexer"].concat(args));
}

function ObjectTypeProperty() {
  for (var _len107 = arguments.length, args = new Array(_len107), _key107 = 0; _key107 < _len107; _key107++) {
    args[_key107] = arguments[_key107];
  }

  return _builder.default.apply(void 0, ["ObjectTypeProperty"].concat(args));
}

function ObjectTypeSpreadProperty() {
  for (var _len108 = arguments.length, args = new Array(_len108), _key108 = 0; _key108 < _len108; _key108++) {
    args[_key108] = arguments[_key108];
  }

  return _builder.default.apply(void 0, ["ObjectTypeSpreadProperty"].concat(args));
}

function OpaqueType() {
  for (var _len109 = arguments.length, args = new Array(_len109), _key109 = 0; _key109 < _len109; _key109++) {
    args[_key109] = arguments[_key109];
  }

  return _builder.default.apply(void 0, ["OpaqueType"].concat(args));
}

function QualifiedTypeIdentifier() {
  for (var _len110 = arguments.length, args = new Array(_len110), _key110 = 0; _key110 < _len110; _key110++) {
    args[_key110] = arguments[_key110];
  }

  return _builder.default.apply(void 0, ["QualifiedTypeIdentifier"].concat(args));
}

function StringLiteralTypeAnnotation() {
  for (var _len111 = arguments.length, args = new Array(_len111), _key111 = 0; _key111 < _len111; _key111++) {
    args[_key111] = arguments[_key111];
  }

  return _builder.default.apply(void 0, ["StringLiteralTypeAnnotation"].concat(args));
}

function StringTypeAnnotation() {
  for (var _len112 = arguments.length, args = new Array(_len112), _key112 = 0; _key112 < _len112; _key112++) {
    args[_key112] = arguments[_key112];
  }

  return _builder.default.apply(void 0, ["StringTypeAnnotation"].concat(args));
}

function ThisTypeAnnotation() {
  for (var _len113 = arguments.length, args = new Array(_len113), _key113 = 0; _key113 < _len113; _key113++) {
    args[_key113] = arguments[_key113];
  }

  return _builder.default.apply(void 0, ["ThisTypeAnnotation"].concat(args));
}

function TupleTypeAnnotation() {
  for (var _len114 = arguments.length, args = new Array(_len114), _key114 = 0; _key114 < _len114; _key114++) {
    args[_key114] = arguments[_key114];
  }

  return _builder.default.apply(void 0, ["TupleTypeAnnotation"].concat(args));
}

function TypeofTypeAnnotation() {
  for (var _len115 = arguments.length, args = new Array(_len115), _key115 = 0; _key115 < _len115; _key115++) {
    args[_key115] = arguments[_key115];
  }

  return _builder.default.apply(void 0, ["TypeofTypeAnnotation"].concat(args));
}

function TypeAlias() {
  for (var _len116 = arguments.length, args = new Array(_len116), _key116 = 0; _key116 < _len116; _key116++) {
    args[_key116] = arguments[_key116];
  }

  return _builder.default.apply(void 0, ["TypeAlias"].concat(args));
}

function TypeAnnotation() {
  for (var _len117 = arguments.length, args = new Array(_len117), _key117 = 0; _key117 < _len117; _key117++) {
    args[_key117] = arguments[_key117];
  }

  return _builder.default.apply(void 0, ["TypeAnnotation"].concat(args));
}

function TypeCastExpression() {
  for (var _len118 = arguments.length, args = new Array(_len118), _key118 = 0; _key118 < _len118; _key118++) {
    args[_key118] = arguments[_key118];
  }

  return _builder.default.apply(void 0, ["TypeCastExpression"].concat(args));
}

function TypeParameter() {
  for (var _len119 = arguments.length, args = new Array(_len119), _key119 = 0; _key119 < _len119; _key119++) {
    args[_key119] = arguments[_key119];
  }

  return _builder.default.apply(void 0, ["TypeParameter"].concat(args));
}

function TypeParameterDeclaration() {
  for (var _len120 = arguments.length, args = new Array(_len120), _key120 = 0; _key120 < _len120; _key120++) {
    args[_key120] = arguments[_key120];
  }

  return _builder.default.apply(void 0, ["TypeParameterDeclaration"].concat(args));
}

function TypeParameterInstantiation() {
  for (var _len121 = arguments.length, args = new Array(_len121), _key121 = 0; _key121 < _len121; _key121++) {
    args[_key121] = arguments[_key121];
  }

  return _builder.default.apply(void 0, ["TypeParameterInstantiation"].concat(args));
}

function UnionTypeAnnotation() {
  for (var _len122 = arguments.length, args = new Array(_len122), _key122 = 0; _key122 < _len122; _key122++) {
    args[_key122] = arguments[_key122];
  }

  return _builder.default.apply(void 0, ["UnionTypeAnnotation"].concat(args));
}

function VoidTypeAnnotation() {
  for (var _len123 = arguments.length, args = new Array(_len123), _key123 = 0; _key123 < _len123; _key123++) {
    args[_key123] = arguments[_key123];
  }

  return _builder.default.apply(void 0, ["VoidTypeAnnotation"].concat(args));
}

function JSXAttribute() {
  for (var _len124 = arguments.length, args = new Array(_len124), _key124 = 0; _key124 < _len124; _key124++) {
    args[_key124] = arguments[_key124];
  }

  return _builder.default.apply(void 0, ["JSXAttribute"].concat(args));
}

function JSXClosingElement() {
  for (var _len125 = arguments.length, args = new Array(_len125), _key125 = 0; _key125 < _len125; _key125++) {
    args[_key125] = arguments[_key125];
  }

  return _builder.default.apply(void 0, ["JSXClosingElement"].concat(args));
}

function JSXElement() {
  for (var _len126 = arguments.length, args = new Array(_len126), _key126 = 0; _key126 < _len126; _key126++) {
    args[_key126] = arguments[_key126];
  }

  return _builder.default.apply(void 0, ["JSXElement"].concat(args));
}

function JSXEmptyExpression() {
  for (var _len127 = arguments.length, args = new Array(_len127), _key127 = 0; _key127 < _len127; _key127++) {
    args[_key127] = arguments[_key127];
  }

  return _builder.default.apply(void 0, ["JSXEmptyExpression"].concat(args));
}

function JSXExpressionContainer() {
  for (var _len128 = arguments.length, args = new Array(_len128), _key128 = 0; _key128 < _len128; _key128++) {
    args[_key128] = arguments[_key128];
  }

  return _builder.default.apply(void 0, ["JSXExpressionContainer"].concat(args));
}

function JSXSpreadChild() {
  for (var _len129 = arguments.length, args = new Array(_len129), _key129 = 0; _key129 < _len129; _key129++) {
    args[_key129] = arguments[_key129];
  }

  return _builder.default.apply(void 0, ["JSXSpreadChild"].concat(args));
}

function JSXIdentifier() {
  for (var _len130 = arguments.length, args = new Array(_len130), _key130 = 0; _key130 < _len130; _key130++) {
    args[_key130] = arguments[_key130];
  }

  return _builder.default.apply(void 0, ["JSXIdentifier"].concat(args));
}

function JSXMemberExpression() {
  for (var _len131 = arguments.length, args = new Array(_len131), _key131 = 0; _key131 < _len131; _key131++) {
    args[_key131] = arguments[_key131];
  }

  return _builder.default.apply(void 0, ["JSXMemberExpression"].concat(args));
}

function JSXNamespacedName() {
  for (var _len132 = arguments.length, args = new Array(_len132), _key132 = 0; _key132 < _len132; _key132++) {
    args[_key132] = arguments[_key132];
  }

  return _builder.default.apply(void 0, ["JSXNamespacedName"].concat(args));
}

function JSXOpeningElement() {
  for (var _len133 = arguments.length, args = new Array(_len133), _key133 = 0; _key133 < _len133; _key133++) {
    args[_key133] = arguments[_key133];
  }

  return _builder.default.apply(void 0, ["JSXOpeningElement"].concat(args));
}

function JSXSpreadAttribute() {
  for (var _len134 = arguments.length, args = new Array(_len134), _key134 = 0; _key134 < _len134; _key134++) {
    args[_key134] = arguments[_key134];
  }

  return _builder.default.apply(void 0, ["JSXSpreadAttribute"].concat(args));
}

function JSXText() {
  for (var _len135 = arguments.length, args = new Array(_len135), _key135 = 0; _key135 < _len135; _key135++) {
    args[_key135] = arguments[_key135];
  }

  return _builder.default.apply(void 0, ["JSXText"].concat(args));
}

function JSXFragment() {
  for (var _len136 = arguments.length, args = new Array(_len136), _key136 = 0; _key136 < _len136; _key136++) {
    args[_key136] = arguments[_key136];
  }

  return _builder.default.apply(void 0, ["JSXFragment"].concat(args));
}

function JSXOpeningFragment() {
  for (var _len137 = arguments.length, args = new Array(_len137), _key137 = 0; _key137 < _len137; _key137++) {
    args[_key137] = arguments[_key137];
  }

  return _builder.default.apply(void 0, ["JSXOpeningFragment"].concat(args));
}

function JSXClosingFragment() {
  for (var _len138 = arguments.length, args = new Array(_len138), _key138 = 0; _key138 < _len138; _key138++) {
    args[_key138] = arguments[_key138];
  }

  return _builder.default.apply(void 0, ["JSXClosingFragment"].concat(args));
}

function Noop() {
  for (var _len139 = arguments.length, args = new Array(_len139), _key139 = 0; _key139 < _len139; _key139++) {
    args[_key139] = arguments[_key139];
  }

  return _builder.default.apply(void 0, ["Noop"].concat(args));
}

function ParenthesizedExpression() {
  for (var _len140 = arguments.length, args = new Array(_len140), _key140 = 0; _key140 < _len140; _key140++) {
    args[_key140] = arguments[_key140];
  }

  return _builder.default.apply(void 0, ["ParenthesizedExpression"].concat(args));
}

function AwaitExpression() {
  for (var _len141 = arguments.length, args = new Array(_len141), _key141 = 0; _key141 < _len141; _key141++) {
    args[_key141] = arguments[_key141];
  }

  return _builder.default.apply(void 0, ["AwaitExpression"].concat(args));
}

function BindExpression() {
  for (var _len142 = arguments.length, args = new Array(_len142), _key142 = 0; _key142 < _len142; _key142++) {
    args[_key142] = arguments[_key142];
  }

  return _builder.default.apply(void 0, ["BindExpression"].concat(args));
}

function ClassProperty() {
  for (var _len143 = arguments.length, args = new Array(_len143), _key143 = 0; _key143 < _len143; _key143++) {
    args[_key143] = arguments[_key143];
  }

  return _builder.default.apply(void 0, ["ClassProperty"].concat(args));
}

function Import() {
  for (var _len144 = arguments.length, args = new Array(_len144), _key144 = 0; _key144 < _len144; _key144++) {
    args[_key144] = arguments[_key144];
  }

  return _builder.default.apply(void 0, ["Import"].concat(args));
}

function Decorator() {
  for (var _len145 = arguments.length, args = new Array(_len145), _key145 = 0; _key145 < _len145; _key145++) {
    args[_key145] = arguments[_key145];
  }

  return _builder.default.apply(void 0, ["Decorator"].concat(args));
}

function DoExpression() {
  for (var _len146 = arguments.length, args = new Array(_len146), _key146 = 0; _key146 < _len146; _key146++) {
    args[_key146] = arguments[_key146];
  }

  return _builder.default.apply(void 0, ["DoExpression"].concat(args));
}

function ExportDefaultSpecifier() {
  for (var _len147 = arguments.length, args = new Array(_len147), _key147 = 0; _key147 < _len147; _key147++) {
    args[_key147] = arguments[_key147];
  }

  return _builder.default.apply(void 0, ["ExportDefaultSpecifier"].concat(args));
}

function ExportNamespaceSpecifier() {
  for (var _len148 = arguments.length, args = new Array(_len148), _key148 = 0; _key148 < _len148; _key148++) {
    args[_key148] = arguments[_key148];
  }

  return _builder.default.apply(void 0, ["ExportNamespaceSpecifier"].concat(args));
}

function TSParameterProperty() {
  for (var _len149 = arguments.length, args = new Array(_len149), _key149 = 0; _key149 < _len149; _key149++) {
    args[_key149] = arguments[_key149];
  }

  return _builder.default.apply(void 0, ["TSParameterProperty"].concat(args));
}

function TSDeclareFunction() {
  for (var _len150 = arguments.length, args = new Array(_len150), _key150 = 0; _key150 < _len150; _key150++) {
    args[_key150] = arguments[_key150];
  }

  return _builder.default.apply(void 0, ["TSDeclareFunction"].concat(args));
}

function TSDeclareMethod() {
  for (var _len151 = arguments.length, args = new Array(_len151), _key151 = 0; _key151 < _len151; _key151++) {
    args[_key151] = arguments[_key151];
  }

  return _builder.default.apply(void 0, ["TSDeclareMethod"].concat(args));
}

function TSQualifiedName() {
  for (var _len152 = arguments.length, args = new Array(_len152), _key152 = 0; _key152 < _len152; _key152++) {
    args[_key152] = arguments[_key152];
  }

  return _builder.default.apply(void 0, ["TSQualifiedName"].concat(args));
}

function TSCallSignatureDeclaration() {
  for (var _len153 = arguments.length, args = new Array(_len153), _key153 = 0; _key153 < _len153; _key153++) {
    args[_key153] = arguments[_key153];
  }

  return _builder.default.apply(void 0, ["TSCallSignatureDeclaration"].concat(args));
}

function TSConstructSignatureDeclaration() {
  for (var _len154 = arguments.length, args = new Array(_len154), _key154 = 0; _key154 < _len154; _key154++) {
    args[_key154] = arguments[_key154];
  }

  return _builder.default.apply(void 0, ["TSConstructSignatureDeclaration"].concat(args));
}

function TSPropertySignature() {
  for (var _len155 = arguments.length, args = new Array(_len155), _key155 = 0; _key155 < _len155; _key155++) {
    args[_key155] = arguments[_key155];
  }

  return _builder.default.apply(void 0, ["TSPropertySignature"].concat(args));
}

function TSMethodSignature() {
  for (var _len156 = arguments.length, args = new Array(_len156), _key156 = 0; _key156 < _len156; _key156++) {
    args[_key156] = arguments[_key156];
  }

  return _builder.default.apply(void 0, ["TSMethodSignature"].concat(args));
}

function TSIndexSignature() {
  for (var _len157 = arguments.length, args = new Array(_len157), _key157 = 0; _key157 < _len157; _key157++) {
    args[_key157] = arguments[_key157];
  }

  return _builder.default.apply(void 0, ["TSIndexSignature"].concat(args));
}

function TSAnyKeyword() {
  for (var _len158 = arguments.length, args = new Array(_len158), _key158 = 0; _key158 < _len158; _key158++) {
    args[_key158] = arguments[_key158];
  }

  return _builder.default.apply(void 0, ["TSAnyKeyword"].concat(args));
}

function TSNumberKeyword() {
  for (var _len159 = arguments.length, args = new Array(_len159), _key159 = 0; _key159 < _len159; _key159++) {
    args[_key159] = arguments[_key159];
  }

  return _builder.default.apply(void 0, ["TSNumberKeyword"].concat(args));
}

function TSObjectKeyword() {
  for (var _len160 = arguments.length, args = new Array(_len160), _key160 = 0; _key160 < _len160; _key160++) {
    args[_key160] = arguments[_key160];
  }

  return _builder.default.apply(void 0, ["TSObjectKeyword"].concat(args));
}

function TSBooleanKeyword() {
  for (var _len161 = arguments.length, args = new Array(_len161), _key161 = 0; _key161 < _len161; _key161++) {
    args[_key161] = arguments[_key161];
  }

  return _builder.default.apply(void 0, ["TSBooleanKeyword"].concat(args));
}

function TSStringKeyword() {
  for (var _len162 = arguments.length, args = new Array(_len162), _key162 = 0; _key162 < _len162; _key162++) {
    args[_key162] = arguments[_key162];
  }

  return _builder.default.apply(void 0, ["TSStringKeyword"].concat(args));
}

function TSSymbolKeyword() {
  for (var _len163 = arguments.length, args = new Array(_len163), _key163 = 0; _key163 < _len163; _key163++) {
    args[_key163] = arguments[_key163];
  }

  return _builder.default.apply(void 0, ["TSSymbolKeyword"].concat(args));
}

function TSVoidKeyword() {
  for (var _len164 = arguments.length, args = new Array(_len164), _key164 = 0; _key164 < _len164; _key164++) {
    args[_key164] = arguments[_key164];
  }

  return _builder.default.apply(void 0, ["TSVoidKeyword"].concat(args));
}

function TSUndefinedKeyword() {
  for (var _len165 = arguments.length, args = new Array(_len165), _key165 = 0; _key165 < _len165; _key165++) {
    args[_key165] = arguments[_key165];
  }

  return _builder.default.apply(void 0, ["TSUndefinedKeyword"].concat(args));
}

function TSNullKeyword() {
  for (var _len166 = arguments.length, args = new Array(_len166), _key166 = 0; _key166 < _len166; _key166++) {
    args[_key166] = arguments[_key166];
  }

  return _builder.default.apply(void 0, ["TSNullKeyword"].concat(args));
}

function TSNeverKeyword() {
  for (var _len167 = arguments.length, args = new Array(_len167), _key167 = 0; _key167 < _len167; _key167++) {
    args[_key167] = arguments[_key167];
  }

  return _builder.default.apply(void 0, ["TSNeverKeyword"].concat(args));
}

function TSThisType() {
  for (var _len168 = arguments.length, args = new Array(_len168), _key168 = 0; _key168 < _len168; _key168++) {
    args[_key168] = arguments[_key168];
  }

  return _builder.default.apply(void 0, ["TSThisType"].concat(args));
}

function TSFunctionType() {
  for (var _len169 = arguments.length, args = new Array(_len169), _key169 = 0; _key169 < _len169; _key169++) {
    args[_key169] = arguments[_key169];
  }

  return _builder.default.apply(void 0, ["TSFunctionType"].concat(args));
}

function TSConstructorType() {
  for (var _len170 = arguments.length, args = new Array(_len170), _key170 = 0; _key170 < _len170; _key170++) {
    args[_key170] = arguments[_key170];
  }

  return _builder.default.apply(void 0, ["TSConstructorType"].concat(args));
}

function TSTypeReference() {
  for (var _len171 = arguments.length, args = new Array(_len171), _key171 = 0; _key171 < _len171; _key171++) {
    args[_key171] = arguments[_key171];
  }

  return _builder.default.apply(void 0, ["TSTypeReference"].concat(args));
}

function TSTypePredicate() {
  for (var _len172 = arguments.length, args = new Array(_len172), _key172 = 0; _key172 < _len172; _key172++) {
    args[_key172] = arguments[_key172];
  }

  return _builder.default.apply(void 0, ["TSTypePredicate"].concat(args));
}

function TSTypeQuery() {
  for (var _len173 = arguments.length, args = new Array(_len173), _key173 = 0; _key173 < _len173; _key173++) {
    args[_key173] = arguments[_key173];
  }

  return _builder.default.apply(void 0, ["TSTypeQuery"].concat(args));
}

function TSTypeLiteral() {
  for (var _len174 = arguments.length, args = new Array(_len174), _key174 = 0; _key174 < _len174; _key174++) {
    args[_key174] = arguments[_key174];
  }

  return _builder.default.apply(void 0, ["TSTypeLiteral"].concat(args));
}

function TSArrayType() {
  for (var _len175 = arguments.length, args = new Array(_len175), _key175 = 0; _key175 < _len175; _key175++) {
    args[_key175] = arguments[_key175];
  }

  return _builder.default.apply(void 0, ["TSArrayType"].concat(args));
}

function TSTupleType() {
  for (var _len176 = arguments.length, args = new Array(_len176), _key176 = 0; _key176 < _len176; _key176++) {
    args[_key176] = arguments[_key176];
  }

  return _builder.default.apply(void 0, ["TSTupleType"].concat(args));
}

function TSUnionType() {
  for (var _len177 = arguments.length, args = new Array(_len177), _key177 = 0; _key177 < _len177; _key177++) {
    args[_key177] = arguments[_key177];
  }

  return _builder.default.apply(void 0, ["TSUnionType"].concat(args));
}

function TSIntersectionType() {
  for (var _len178 = arguments.length, args = new Array(_len178), _key178 = 0; _key178 < _len178; _key178++) {
    args[_key178] = arguments[_key178];
  }

  return _builder.default.apply(void 0, ["TSIntersectionType"].concat(args));
}

function TSParenthesizedType() {
  for (var _len179 = arguments.length, args = new Array(_len179), _key179 = 0; _key179 < _len179; _key179++) {
    args[_key179] = arguments[_key179];
  }

  return _builder.default.apply(void 0, ["TSParenthesizedType"].concat(args));
}

function TSTypeOperator() {
  for (var _len180 = arguments.length, args = new Array(_len180), _key180 = 0; _key180 < _len180; _key180++) {
    args[_key180] = arguments[_key180];
  }

  return _builder.default.apply(void 0, ["TSTypeOperator"].concat(args));
}

function TSIndexedAccessType() {
  for (var _len181 = arguments.length, args = new Array(_len181), _key181 = 0; _key181 < _len181; _key181++) {
    args[_key181] = arguments[_key181];
  }

  return _builder.default.apply(void 0, ["TSIndexedAccessType"].concat(args));
}

function TSMappedType() {
  for (var _len182 = arguments.length, args = new Array(_len182), _key182 = 0; _key182 < _len182; _key182++) {
    args[_key182] = arguments[_key182];
  }

  return _builder.default.apply(void 0, ["TSMappedType"].concat(args));
}

function TSLiteralType() {
  for (var _len183 = arguments.length, args = new Array(_len183), _key183 = 0; _key183 < _len183; _key183++) {
    args[_key183] = arguments[_key183];
  }

  return _builder.default.apply(void 0, ["TSLiteralType"].concat(args));
}

function TSExpressionWithTypeArguments() {
  for (var _len184 = arguments.length, args = new Array(_len184), _key184 = 0; _key184 < _len184; _key184++) {
    args[_key184] = arguments[_key184];
  }

  return _builder.default.apply(void 0, ["TSExpressionWithTypeArguments"].concat(args));
}

function TSInterfaceDeclaration() {
  for (var _len185 = arguments.length, args = new Array(_len185), _key185 = 0; _key185 < _len185; _key185++) {
    args[_key185] = arguments[_key185];
  }

  return _builder.default.apply(void 0, ["TSInterfaceDeclaration"].concat(args));
}

function TSInterfaceBody() {
  for (var _len186 = arguments.length, args = new Array(_len186), _key186 = 0; _key186 < _len186; _key186++) {
    args[_key186] = arguments[_key186];
  }

  return _builder.default.apply(void 0, ["TSInterfaceBody"].concat(args));
}

function TSTypeAliasDeclaration() {
  for (var _len187 = arguments.length, args = new Array(_len187), _key187 = 0; _key187 < _len187; _key187++) {
    args[_key187] = arguments[_key187];
  }

  return _builder.default.apply(void 0, ["TSTypeAliasDeclaration"].concat(args));
}

function TSAsExpression() {
  for (var _len188 = arguments.length, args = new Array(_len188), _key188 = 0; _key188 < _len188; _key188++) {
    args[_key188] = arguments[_key188];
  }

  return _builder.default.apply(void 0, ["TSAsExpression"].concat(args));
}

function TSTypeAssertion() {
  for (var _len189 = arguments.length, args = new Array(_len189), _key189 = 0; _key189 < _len189; _key189++) {
    args[_key189] = arguments[_key189];
  }

  return _builder.default.apply(void 0, ["TSTypeAssertion"].concat(args));
}

function TSEnumDeclaration() {
  for (var _len190 = arguments.length, args = new Array(_len190), _key190 = 0; _key190 < _len190; _key190++) {
    args[_key190] = arguments[_key190];
  }

  return _builder.default.apply(void 0, ["TSEnumDeclaration"].concat(args));
}

function TSEnumMember() {
  for (var _len191 = arguments.length, args = new Array(_len191), _key191 = 0; _key191 < _len191; _key191++) {
    args[_key191] = arguments[_key191];
  }

  return _builder.default.apply(void 0, ["TSEnumMember"].concat(args));
}

function TSModuleDeclaration() {
  for (var _len192 = arguments.length, args = new Array(_len192), _key192 = 0; _key192 < _len192; _key192++) {
    args[_key192] = arguments[_key192];
  }

  return _builder.default.apply(void 0, ["TSModuleDeclaration"].concat(args));
}

function TSModuleBlock() {
  for (var _len193 = arguments.length, args = new Array(_len193), _key193 = 0; _key193 < _len193; _key193++) {
    args[_key193] = arguments[_key193];
  }

  return _builder.default.apply(void 0, ["TSModuleBlock"].concat(args));
}

function TSImportEqualsDeclaration() {
  for (var _len194 = arguments.length, args = new Array(_len194), _key194 = 0; _key194 < _len194; _key194++) {
    args[_key194] = arguments[_key194];
  }

  return _builder.default.apply(void 0, ["TSImportEqualsDeclaration"].concat(args));
}

function TSExternalModuleReference() {
  for (var _len195 = arguments.length, args = new Array(_len195), _key195 = 0; _key195 < _len195; _key195++) {
    args[_key195] = arguments[_key195];
  }

  return _builder.default.apply(void 0, ["TSExternalModuleReference"].concat(args));
}

function TSNonNullExpression() {
  for (var _len196 = arguments.length, args = new Array(_len196), _key196 = 0; _key196 < _len196; _key196++) {
    args[_key196] = arguments[_key196];
  }

  return _builder.default.apply(void 0, ["TSNonNullExpression"].concat(args));
}

function TSExportAssignment() {
  for (var _len197 = arguments.length, args = new Array(_len197), _key197 = 0; _key197 < _len197; _key197++) {
    args[_key197] = arguments[_key197];
  }

  return _builder.default.apply(void 0, ["TSExportAssignment"].concat(args));
}

function TSNamespaceExportDeclaration() {
  for (var _len198 = arguments.length, args = new Array(_len198), _key198 = 0; _key198 < _len198; _key198++) {
    args[_key198] = arguments[_key198];
  }

  return _builder.default.apply(void 0, ["TSNamespaceExportDeclaration"].concat(args));
}

function TSTypeAnnotation() {
  for (var _len199 = arguments.length, args = new Array(_len199), _key199 = 0; _key199 < _len199; _key199++) {
    args[_key199] = arguments[_key199];
  }

  return _builder.default.apply(void 0, ["TSTypeAnnotation"].concat(args));
}

function TSTypeParameterInstantiation() {
  for (var _len200 = arguments.length, args = new Array(_len200), _key200 = 0; _key200 < _len200; _key200++) {
    args[_key200] = arguments[_key200];
  }

  return _builder.default.apply(void 0, ["TSTypeParameterInstantiation"].concat(args));
}

function TSTypeParameterDeclaration() {
  for (var _len201 = arguments.length, args = new Array(_len201), _key201 = 0; _key201 < _len201; _key201++) {
    args[_key201] = arguments[_key201];
  }

  return _builder.default.apply(void 0, ["TSTypeParameterDeclaration"].concat(args));
}

function TSTypeParameter() {
  for (var _len202 = arguments.length, args = new Array(_len202), _key202 = 0; _key202 < _len202; _key202++) {
    args[_key202] = arguments[_key202];
  }

  return _builder.default.apply(void 0, ["TSTypeParameter"].concat(args));
}

function NumberLiteral() {
  console.trace("The node type NumberLiteral has been renamed to NumericLiteral");

  for (var _len203 = arguments.length, args = new Array(_len203), _key203 = 0; _key203 < _len203; _key203++) {
    args[_key203] = arguments[_key203];
  }

  return NumberLiteral.apply(void 0, ["NumberLiteral"].concat(args));
}

function RegexLiteral() {
  console.trace("The node type RegexLiteral has been renamed to RegExpLiteral");

  for (var _len204 = arguments.length, args = new Array(_len204), _key204 = 0; _key204 < _len204; _key204++) {
    args[_key204] = arguments[_key204];
  }

  return RegexLiteral.apply(void 0, ["RegexLiteral"].concat(args));
}

function RestProperty() {
  console.trace("The node type RestProperty has been renamed to RestElement");

  for (var _len205 = arguments.length, args = new Array(_len205), _key205 = 0; _key205 < _len205; _key205++) {
    args[_key205] = arguments[_key205];
  }

  return RestProperty.apply(void 0, ["RestProperty"].concat(args));
}

function SpreadProperty() {
  console.trace("The node type SpreadProperty has been renamed to SpreadElement");

  for (var _len206 = arguments.length, args = new Array(_len206), _key206 = 0; _key206 < _len206; _key206++) {
    args[_key206] = arguments[_key206];
  }

  return SpreadProperty.apply(void 0, ["SpreadProperty"].concat(args));
}