<template>
  <view class="container">
    <view class="page-title">图片地址提取工具</view>

    <!-- HTML代码输入区域 -->
    <view class="section">
      <view class="section-title">
        HTML代码输入
        <view class="title-buttons">
          <button class="btn small" @click="loadTestData">加载测试数据</button>
          <button class="clear-btn" @click="clearHtmlInput">清空</button>
        </view>
      </view>
      <textarea
        v-model="htmlInput"
        class="html-input"
        placeholder="请输入HTML代码..."
        @input="parseHtml"
      ></textarea>

      <!-- 解析结果提示 -->
      <view class="parse-result" v-if="htmlInput.trim()">
        <text class="result-text">
          解析结果: 轮播图片 {{ swiperImages.length }} 张, 详情图片
          {{ rtfImages.length }} 张
        </text>
      </view>
    </view>

    <!-- uni-swiper-wrapper 图片表格 -->
    <view class="section" v-if="swiperImages.length > 0">
      <view class="section-title">轮播图片 (uni-swiper-wrapper)</view>

      <!-- 货号填充控制 -->
      <view class="control-row">
        <input
          v-model="mainProductCode"
          class="input-field"
          placeholder="输入主货号"
        />
        <button class="btn" @click="fillProductCodes">填充货号</button>
        <button class="btn copy-all-btn" @click="copyAllSwiperUrls">
          一键复制图片地址
        </button>
      </view>

      <!-- 表格 -->
      <view class="table">
        <view class="table-header">
          <view class="col-image">图片</view>
          <view class="col-code">货号</view>
          <view class="col-color">颜色</view>
          <view class="col-action">操作</view>
        </view>
        <view
          v-for="(item, index) in swiperImages"
          :key="index"
          class="table-row"
        >
          <view class="col-image">
            <image :src="item.url" class="preview-image"></image>
          </view>
          <view class="col-code">
            <input
              v-model="item.productCode"
              class="input-field small"
              placeholder="货号"
            />
            <button class="copy-btn" @click="copyText(item.productCode)">
              复制
            </button>
          </view>
          <view class="col-color">
            <input
              v-model="item.color"
              class="input-field small"
              placeholder="颜色"
            />
          </view>
          <view class="col-action">
            <button class="btn small" @click="copyText(item.url)">
              复制地址
            </button>
            <button
              class="btn small delete-btn"
              @click="deleteSwiperRow(index)"
            >
              删除
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- rtf64 图片表格 -->
    <view class="section" v-if="rtfImages.length > 0">
      <view class="section-title">详情图片 (rtf64)</view>

      <!-- 颜色填充控制 -->
      <view class="control-row">
        <input v-model="fillColor" class="input-field" placeholder="输入颜色" />
        <button class="btn" @click="fillAllColors">一键填充颜色</button>
        <button class="btn copy-all-btn" @click="copyAllRtfUrls">
          一键复制图片地址
        </button>
      </view>

      <!-- 表格 -->
      <view class="table">
        <view class="table-header">
          <view class="col-image">图片</view>
          <view class="col-color">颜色</view>
          <view class="col-action">操作</view>
        </view>
        <view v-for="(item, index) in rtfImages" :key="index" class="table-row">
          <view class="col-image">
            <image :src="item.url" class="preview-image"></image>
          </view>
          <view class="col-color">
            <input
              v-model="item.color"
              class="input-field small"
              placeholder="颜色"
            />
            <button class="copy-btn" @click="copyText(item.color)">复制</button>
          </view>
          <view class="col-action">
            <button class="btn small" @click="copyText(item.url)">
              复制地址
            </button>
            <button class="btn small delete-btn" @click="deleteRtfRow(index)">
              删除
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- JSON生成区域 -->
    <view class="section">
      <view class="section-title">JSON生成 (blocks数组)</view>

      <view class="control-row">
        <button class="clear-btn" @click="clearImageUrls">清空</button>
        <button class="btn" @click="copyJsonResult">复制JSON</button>
      </view>

      <textarea
        v-model="imageUrlsInput"
        class="image-urls-input"
        placeholder="请输入图片地址，每行一个..."
        @input="generateJson"
      ></textarea>

      <view class="json-result">
        <text class="json-title">生成的JSON:</text>
        <text class="json-content">{{ jsonResult }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      htmlInput: "",
      swiperImages: [],
      rtfImages: [],
      mainProductCode: "",
      fillColor: "",
      imageUrlsInput: "",
      jsonResult: "",
      span: `<uni-page data-page="pages/goods/goods-detail/index"><!----><!----><uni-page-wrapper><uni-page-body><uni-view data-v-025e4f16="" class="padding-bottom"><uni-view data-v-025e4f16="" class="cu-custom" style="height: 0px;"><uni-view data-v-025e4f16="" class="cu-bar fixed" style="padding-top: 0px; height: 45px;"><uni-navigator data-v-025e4f16="" class="bg-black-black round margin-lr" style="padding: 5px 6px;"><uni-text data-v-025e4f16="" class="cuIcon-home"><span>首页</span></uni-text></uni-navigator></uni-view></uni-view><uni-view data-v-025e4f16="" class="product-bg margin-bottom-xs"><uni-swiper data-v-025e4f16="" class="screen-swiper square-dot"><div class="uni-swiper-wrapper"><div class="uni-swiper-slides"><div class="uni-swiper-slide-frame" style="width: 100%; height: 100%; transform: translate(2.0736%, 0px) translateZ(0px);"><uni-swiper-item data-v-025e4f16="" style="position: absolute; width: 100%; height: 100%; transform: translate(0%, 0px) translateZ(0px);"><uni-image data-v-025e4f16=""><div style="background-image: url(&quot;https://oss.dsxgj.com/spu_images/621016825/1/01.jpg?x-oss-process=style/d1&quot;); background-position: center center; background-size: cover; background-repeat: no-repeat;"></div><!----><img src="https://oss.dsxgj.com/spu_images/621016825/1/01.jpg?x-oss-process=style/d1" draggable="false"></uni-image></uni-swiper-item><uni-swiper-item data-v-025e4f16="" style="position: absolute; width: 100%; height: 100%; transform: translate(100%, 0px) translateZ(0px);"><uni-image data-v-025e4f16=""><div style="background-image: url(&quot;https://oss.dsxgj.com/spu_images/621016825/1/02.jpg?x-oss-process=style/d1&quot;); background-position: center center; background-size: cover; background-repeat: no-repeat;"></div><!----><img src="https://oss.dsxgj.com/spu_images/621016825/1/02.jpg?x-oss-process=style/d1" draggable="false"></uni-image></uni-swiper-item><uni-swiper-item data-v-025e4f16="" style="position: absolute; width: 100%; height: 100%; transform: translate(200%, 0px) translateZ(0px);"><uni-image data-v-025e4f16=""><div style="background-image: url(&quot;https://oss.dsxgj.com/spu_images/621016825/1/03.jpg?x-oss-process=style/d1&quot;); background-position: center center; background-size: cover; background-repeat: no-repeat;"></div><!----><img src="https://oss.dsxgj.com/spu_images/621016825/1/03.jpg?x-oss-process=style/d1" draggable="false"></uni-image></uni-swiper-item><uni-swiper-item data-v-025e4f16="" style="position: absolute; width: 100%; height: 100%; transform: translate(-300%, 0px) translateZ(0px);"><uni-image data-v-025e4f16=""><div style="background-image: url(&quot;https://oss.dsxgj.com/spu_images/621016825/1/04.jpg?x-oss-process=style/d1&quot;); background-position: center center; background-size: cover; background-repeat: no-repeat;"></div><!----><img src="https://oss.dsxgj.com/spu_images/621016825/1/04.jpg?x-oss-process=style/d1" draggable="false"></uni-image></uni-swiper-item><uni-swiper-item data-v-025e4f16="" style="position: absolute; width: 100%; height: 100%; transform: translate(-200%, 0px) translateZ(0px);"><uni-image data-v-025e4f16=""><div style="background-image: url(&quot;https://oss.dsxgj.com/spu_images/621016825/1/05.jpg?x-oss-process=style/d1&quot;); background-position: center center; background-size: cover; background-repeat: no-repeat;"></div><!----><img src="https://oss.dsxgj.com/spu_images/621016825/1/05.jpg?x-oss-process=style/d1" draggable="false"></uni-image></uni-swiper-item><uni-swiper-item data-v-025e4f16="" style="position: absolute; width: 100%; height: 100%; transform: translate(-100%, 0px) translateZ(0px);"><uni-image data-v-025e4f16=""><div style="background-image: url(&quot;https://oss.dsxgj.com/spu_images/621016825/1/06.jpg?x-oss-process=style/d1&quot;); background-position: center center; background-size: cover; background-repeat: no-repeat;"></div><!----><img src="https://oss.dsxgj.com/spu_images/621016825/1/06.jpg?x-oss-process=style/d1" draggable="false"></uni-image></uni-swiper-item></div></div><div class="uni-swiper-dots uni-swiper-dots-horizontal"><div class="uni-swiper-dot uni-swiper-dot-active" style="background: rgb(51, 51, 51);"></div><div class="uni-swiper-dot" style="background: rgb(204, 204, 204);"></div><div class="uni-swiper-dot" style="background: rgb(204, 204, 204);"></div><div class="uni-swiper-dot" style="background: rgb(204, 204, 204);"></div><div class="uni-swiper-dot" style="background: rgb(204, 204, 204);"></div><div class="uni-swiper-dot" style="background: rgb(204, 204, 204);"></div></div></div></uni-swiper></uni-view><uni-view data-v-025e4f16="" class="padding-lr-sm"><uni-view data-v-025e4f16="" class="goods-activeBackground"><uni-view data-v-025e4f16="" class="padding-sm radius-lg"><uni-view data-v-025e4f16="" class="text-xxl"><uni-text data-v-025e4f16="" class="text-lg"><span>出库价:</span></uni-text><uni-text data-v-025e4f16="" class="text-price text-scarlet text-bold"><span>7.2</span></uni-text><!----><!----><!----><!----><uni-text data-v-025e4f16="" class="margin-left-sm text-lg"><span>订购倍数：3</span></uni-text></uni-view><uni-view data-v-025e4f16="" class="text-xxl"><uni-text data-v-025e4f16="" class="text-lg"><span>零售价:</span></uni-text><uni-text data-v-025e4f16="" class="text-lg text-price"><span>15</span></uni-text><!----><!----></uni-view><!----></uni-view></uni-view><uni-view data-v-025e4f16="" class="bg-white padding-sm margin-top-xs radius-lg" style="user-select: text;"><uni-view data-v-025e4f16="" class="text-xl text-bold"><uni-text data-v-025e4f16="" class="text-black"><span>经典内裤.中版- 款号：621016825</span></uni-text></uni-view><uni-view data-v-025e4f16="" class="text-df padding-tb-xs"><uni-text data-v-025e4f16="" class="text-gray"><span>莫代尔面料，柔软舒适，吸湿透气，款式简约大方，前置蝴蝶结点缀，可爱精致，腰头裤脚柔软，不勒痕不卡裆，10A抗菌蚕丝底裆，呵护健康</span></uni-text></uni-view><!----><uni-view data-v-025e4f16="" class="flex align-center text-gray justify-between margin-top-sm text-df"><uni-view data-v-025e4f16="" class="text-gray text-right">销存记录<uni-text data-v-025e4f16="" class="cuIcon-right"><span></span></uni-text></uni-view><uni-view data-v-025e4f16=""><uni-text data-v-025e4f16="" class="cuIcon-favor"><span></span></uni-text><uni-text data-v-025e4f16="" class="margin-left-xs"><span>收藏</span></uni-text></uni-view><uni-view data-v-025e4f16=""><uni-text data-v-025e4f16="" class="cuIcon-forward"><span></span></uni-text><uni-text data-v-025e4f16="" class="margin-left-xs"><span>分享</span></uni-text></uni-view></uni-view></uni-view><uni-view data-v-025e4f16="" class="bg-white radius-lg margin-top-xs text-df"><uni-view data-v-795360c6="" data-v-025e4f16="" catchtouchmove="touchMove" class="cu-modal bottom-modal"><uni-view data-v-795360c6="" class="cu-dialog bg-white  "><uni-view data-v-795360c6="" class="article"><uni-view data-v-795360c6="" class="cu-item"><uni-view data-v-795360c6="" class="content text-lg margin-top-sm">优惠券</uni-view></uni-view><uni-view data-v-795360c6="" class="text-xl close-icon"><uni-text data-v-795360c6="" class="cuIcon-close"><span></span></uni-text></uni-view></uni-view><uni-scroll-view data-v-795360c6="" style="max-height: 70vh;"><div class="uni-scroll-view"><div class="uni-scroll-view" style="overflow: hidden auto;"><div class="uni-scroll-view-content"><!----><uni-view data-v-795360c6="" class="cu-list text-left padding-bottom padding-top-sm padding-lr-sm"><uni-view data-v-795360c6="" class="cu-load over"></uni-view></uni-view></div></div></div></uni-scroll-view></uni-view></uni-view><uni-view data-v-025e4f16="" class="bg-white card-radius padding-tb-sm"><uni-view data-v-025e4f16="" class="flex"><uni-view data-v-025e4f16="" class="flex-sub"><uni-view data-v-025e4f16="" class="text-gray margin-left-sm">选择</uni-view></uni-view><uni-view data-v-025e4f16="" class="flex-treble text-black"><uni-view data-v-025e4f16="" class="display-ib"><uni-view data-v-025e4f16="" class="display-ib">颜色</uni-view><!----><uni-view data-v-025e4f16="" class="display-ib">,</uni-view></uni-view><uni-view data-v-025e4f16="" class="display-ib"><uni-view data-v-025e4f16="" class="display-ib">尺码</uni-view><!----><!----></uni-view></uni-view><uni-view data-v-025e4f16="" class="flex-sub text-gray text-right"><uni-text data-v-025e4f16="" class="cuIcon-right margin-right-sm"><span></span></uni-text></uni-view></uni-view></uni-view></uni-view><!----><uni-view data-v-025e4f16="" class="cu-bar bg-white margin-top-xs"><uni-view data-v-025e4f16="" class="content">商家推荐</uni-view></uni-view><uni-view data-v-025e4f16="" class="padding-xs"><uni-view data-v-378cf9a9="" data-v-025e4f16="" class="goods-container flex"><uni-view data-v-378cf9a9="" class="goods-box"><uni-navigator data-v-378cf9a9="" class="goods-item"><!----><uni-view data-v-378cf9a9="" class="goods-absolute"><uni-view data-v-378cf9a9="" class="img-box"><uni-image data-v-378cf9a9=""><div style="background-image: url(&quot;https://oss.dsxgj.com/spu_images/632034381/1/01.jpg?x-oss-process=style/d1&quot;); background-position: 0% 0%; background-size: 100% 100%; background-repeat: no-repeat;"></div><!----><img src="https://oss.dsxgj.com/spu_images/632034381/1/01.jpg?x-oss-process=style/d1" draggable="false"></uni-image></uni-view><uni-view data-v-378cf9a9="" class="text-black text-df padding-lr-sm overflow-1">女家居套装</uni-view><uni-view data-v-378cf9a9="" class=" text-sm text-gray padding-left-sm overflow-1">632034381</uni-view><uni-view data-v-378cf9a9="" class="flex justify-between align-center padding-left-sm"><uni-view data-v-378cf9a9="" class="flex align-center"><uni-text data-v-378cf9a9="" class="text-red text-xl"><span>￥65.7</span></uni-text></uni-view></uni-view></uni-view></uni-navigator></uni-view><uni-view data-v-378cf9a9="" class="goods-box"><uni-navigator data-v-378cf9a9="" class="goods-item"><!----><uni-view data-v-378cf9a9="" class="goods-absolute"><uni-view data-v-378cf9a9="" class="img-box"><uni-image data-v-378cf9a9=""><div style="background-image: url(&quot;https://oss.dsxgj.com/spu_images/684045801/1/01.jpg?x-oss-process=style/d1&quot;); background-position: 0% 0%; background-size: 100% 100%; background-repeat: no-repeat;"></div><!----><img src="https://oss.dsxgj.com/spu_images/684045801/1/01.jpg?x-oss-process=style/d1" draggable="false"></uni-image></uni-view><uni-view data-v-378cf9a9="" class="text-black text-df padding-lr-sm overflow-1">女睡裙</uni-view><uni-view data-v-378cf9a9="" class=" text-sm text-gray padding-left-sm overflow-1">684045801</uni-view><uni-view data-v-378cf9a9="" class="flex justify-between align-center padding-left-sm"><uni-view data-v-378cf9a9="" class="flex align-center"><uni-text data-v-378cf9a9="" class="text-red text-xl"><span>￥20.7</span></uni-text></uni-view></uni-view></uni-view></uni-navigator></uni-view><uni-view data-v-378cf9a9="" class="goods-box"><uni-navigator data-v-378cf9a9="" class="goods-item"><!----><uni-view data-v-378cf9a9="" class="goods-absolute"><uni-view data-v-378cf9a9="" class="img-box"><uni-image data-v-378cf9a9=""><div style="background-image: url(&quot;https://oss.dsxgj.com/spu_images/632036312/1/01.jpg?x-oss-process=style/d1&quot;); background-position: 0% 0%; background-size: 100% 100%; background-repeat: no-repeat;"></div><!----><img src="https://oss.dsxgj.com/spu_images/632036312/1/01.jpg?x-oss-process=style/d1" draggable="false"></uni-image></uni-view><uni-view data-v-378cf9a9="" class="text-black text-df padding-lr-sm overflow-1">女家居套装</uni-view><uni-view data-v-378cf9a9="" class=" text-sm text-gray padding-left-sm overflow-1">632036312</uni-view><uni-view data-v-378cf9a9="" class="flex justify-between align-center padding-left-sm"><uni-view data-v-378cf9a9="" class="flex align-center"><uni-text data-v-378cf9a9="" class="text-red text-xl"><span>￥47.2</span></uni-text></uni-view></uni-view></uni-view></uni-navigator></uni-view><uni-view data-v-378cf9a9="" class="goods-box"><uni-navigator data-v-378cf9a9="" class="goods-item"><!----><uni-view data-v-378cf9a9="" class="goods-absolute"><uni-view data-v-378cf9a9="" class="img-box"><uni-image data-v-378cf9a9=""><div style="background-image: url(&quot;https://oss.dsxgj.com/spu_images/665065805/1/01.jpg?x-oss-process=style/d1&quot;); background-position: 0% 0%; background-size: 100% 100%; background-repeat: no-repeat;"></div><!----><img src="https://oss.dsxgj.com/spu_images/665065805/1/01.jpg?x-oss-process=style/d1" draggable="false"></uni-image></uni-view><uni-view data-v-378cf9a9="" class="text-black text-df padding-lr-sm overflow-1">女童内裤</uni-view><uni-view data-v-378cf9a9="" class=" text-sm text-gray padding-left-sm overflow-1">665065805</uni-view><uni-view data-v-378cf9a9="" class="flex justify-between align-center padding-left-sm"><uni-view data-v-378cf9a9="" class="flex align-center"><uni-text data-v-378cf9a9="" class="text-red text-xl"><span>￥5.8</span></uni-text></uni-view></uni-view></uni-view></uni-navigator></uni-view><uni-view data-v-378cf9a9="" class="goods-box"><uni-navigator data-v-378cf9a9="" class="goods-item"><!----><uni-view data-v-378cf9a9="" class="goods-absolute"><uni-view data-v-378cf9a9="" class="img-box"><uni-image data-v-378cf9a9=""><div style="background-image: url(&quot;https://oss.dsxgj.com/spu_images/632066333/1/01.jpg?x-oss-process=style/d1&quot;); background-position: 0% 0%; background-size: 100% 100%; background-repeat: no-repeat;"></div><!----><img src="https://oss.dsxgj.com/spu_images/632066333/1/01.jpg?x-oss-process=style/d1" draggable="false"></uni-image></uni-view><uni-view data-v-378cf9a9="" class="text-black text-df padding-lr-sm overflow-1">女睡裙</uni-view><uni-view data-v-378cf9a9="" class=" text-sm text-gray padding-left-sm overflow-1">632066333</uni-view><uni-view data-v-378cf9a9="" class="flex justify-between align-center padding-left-sm"><uni-view data-v-378cf9a9="" class="flex align-center"><uni-text data-v-378cf9a9="" class="text-red text-xl"><span>￥17.2</span></uni-text></uni-view></uni-view></uni-view></uni-navigator></uni-view><uni-view data-v-378cf9a9="" class="goods-box"><uni-navigator data-v-378cf9a9="" class="goods-item"><!----><uni-view data-v-378cf9a9="" class="goods-absolute"><uni-view data-v-378cf9a9="" class="img-box"><uni-image data-v-378cf9a9=""><div style="background-image: url(&quot;https://oss.dsxgj.com/spu_images/665066308/1/01.jpg?x-oss-process=style/d1&quot;); background-position: 0% 0%; background-size: 100% 100%; background-repeat: no-repeat;"></div><!----><img src="https://oss.dsxgj.com/spu_images/665066308/1/01.jpg?x-oss-process=style/d1" draggable="false"></uni-image></uni-view><uni-view data-v-378cf9a9="" class="text-black text-df padding-lr-sm overflow-1">女童内裤</uni-view><uni-view data-v-378cf9a9="" class=" text-sm text-gray padding-left-sm overflow-1">665066308</uni-view><uni-view data-v-378cf9a9="" class="flex justify-between align-center padding-left-sm"><uni-view data-v-378cf9a9="" class="flex align-center"><uni-text data-v-378cf9a9="" class="text-red text-xl"><span>￥8.6</span></uni-text></uni-view></uni-view></uni-view></uni-navigator></uni-view></uni-view></uni-view><!----><uni-view data-v-025e4f16="" class="cu-bar bg-white margin-top-xs"><uni-view data-v-025e4f16="" class="content">商品属性</uni-view></uni-view><uni-view data-v-f337b01c="" data-v-025e4f16="" class="padding-sm bg-white"><uni-view data-v-f337b01c="" class="goods-attrs-wrapper grid col-2"><uni-view data-v-f337b01c="" class="item-wrapper"><uni-text data-v-f337b01c="" class="label" style="background-color: rgb(245, 245, 245);"><span>面料</span></uni-text><uni-text data-v-f337b01c="" class="value"><span>莫代尔</span></uni-text></uni-view><uni-view data-v-f337b01c="" class="item-wrapper"><uni-text data-v-f337b01c="" class="label" style="background-color: rgb(245, 245, 245);"><span>版型</span></uni-text><uni-text data-v-f337b01c="" class="value"><span>内裤-三角中版</span></uni-text></uni-view><uni-view data-v-f337b01c="" class="item-wrapper"><uni-text data-v-f337b01c="" class="label" style="background-color: rgb(245, 245, 245);"><span>组合价</span></uni-text><uni-text data-v-f337b01c="" class="value"><span>39=3</span></uni-text></uni-view><uni-view data-v-f337b01c="" class="item-wrapper"><uni-text data-v-f337b01c="" class="label" style="background-color: transparent;"><span></span></uni-text><uni-text data-v-f337b01c="" class="value"><span></span></uni-text></uni-view></uni-view></uni-view><uni-view data-v-025e4f16="" class="cu-bar bg-white margin-top-xs radius-lg"><uni-view data-v-025e4f16="" class="content">商品信息</uni-view></uni-view><uni-view data-v-025e4f16="" class="bg-white"><uni-view data-v-b20f91f6="" data-v-025e4f16="" style="display: inherit;"><!----><uni-view data-v-b20f91f6="" class="top"><div data-v-b20f91f6="" id="rtf64"><div><style scoped="true">@keyframes show{0%{opacity:0}100%{opacity:1}}img{max-width:100%}</style><p><img src="https://oss.dsxgj.com/spu_images/621016825/2/1_01.jpg?x-oss-process=style/d1" style="max-width: 100%; vertical-align: middle;"><img src="https://oss.dsxgj.com/spu_images/621016825/2/1_02.jpg?x-oss-process=style/d1" style="max-width: 100%; vertical-align: middle;"><img src="https://oss.dsxgj.com/spu_images/621016825/2/1_03.jpg?x-oss-process=style/d1" style="max-width: 100%; vertical-align: middle;"><img src="https://oss.dsxgj.com/spu_images/621016825/2/1_04.jpg?x-oss-process=style/d1" style="max-width: 100%; vertical-align: middle;"><img src="https://oss.dsxgj.com/spu_images/621016825/2/1_05.jpg?x-oss-process=style/d1" style="max-width: 100%; vertical-align: middle;"><img src="https://oss.dsxgj.com/spu_images/621016825/2/1_06.jpg?x-oss-process=style/d1" style="max-width: 100%; vertical-align: middle;"><img src="https://oss.dsxgj.com/spu_images/621016825/2/1_07.jpg?x-oss-process=style/d1" style="max-width: 100%; vertical-align: middle;"></p></div></div></uni-view></uni-view></uni-view><uni-view data-v-025e4f16="" class="cu-load bg-gray to-down">已经到底啦...</uni-view></uni-view><uni-view data-v-025e4f16="" class="cu-bar bg-white tabbar border shop foot"><!----><uni-view data-v-025e4f16="" class="action bg-white"><uni-view data-v-025e4f16="" class="cuIcon-home" style="font-size: 20px;"></uni-view>首页</uni-view><uni-navigator data-v-025e4f16="" class="action"><uni-view data-v-025e4f16="" class="cuIcon-cart" style="font-size: 20px;"><uni-view data-v-025e4f16="" class="cu-tag badge">5</uni-view></uni-view>购物车统计</uni-navigator><uni-navigator data-v-025e4f16="" class="action" style="margin-left: 10px;"><uni-view data-v-025e4f16="" class="cuIcon-footprint" style="font-size: 20px;"></uni-view>我的足迹</uni-navigator><uni-view data-v-025e4f16="" class="btn-group" style="justify-content: flex-end;"><uni-button data-v-025e4f16="" class="cu-btn bg-red round margin-left-sm" data-type="1">加入购物车</uni-button></uni-view></uni-view><uni-view data-v-658fd12a="" data-v-025e4f16=""><uni-view data-v-658fd12a="" catchtouchmove="touchMove" class="cu-modal bottom-modal"><uni-view data-v-658fd12a="" class="cu-dialog dialo-sku bg-white"><uni-view data-v-658fd12a="" class="list-wrapper"><uni-view data-v-658fd12a="" class="header-wrapper" style="height: 40px;"><uni-text data-v-658fd12a="" style="width: 12%;"><span>颜色</span></uni-text><uni-text data-v-658fd12a="" style="width: 12%;"><span>尺码</span></uni-text><uni-text data-v-658fd12a="" style="width: 12%;"><span>库存</span></uni-text><uni-text data-v-658fd12a="" style="width: 40%;"><span>订购数量</span></uni-text><uni-text data-v-658fd12a="" style="width: 12%;"><span>店库存</span></uni-text><uni-text data-v-658fd12a="" style="width: 12%;"><span>已加购</span></uni-text></uni-view><uni-view data-v-658fd12a="" class="items-wrapper"><uni-scroll-view data-v-658fd12a="" style="height: 100%;"><div class="uni-scroll-view"><div class="uni-scroll-view" style="overflow: hidden auto;"><div class="uni-scroll-view-content"><!----><uni-view data-v-658fd12a="" class="header-wrapper"><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a=""><span>贝壳粉</span></uni-text></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a=""><span>F</span></uni-text></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a="" style="color: rgb(65, 159, 49);"><span>有货</span></uni-text></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 40%;"><uni-view data-v-658fd12a="" class="num-wrapper"><uni-text data-v-658fd12a="" class="num-button icon cuIcon-move "><span></span></uni-text><uni-text data-v-658fd12a="" class="input-num" style="width: 42px;"><span>3</span></uni-text><uni-text data-v-658fd12a="" class="num-button icon cuIcon-add "><span></span></uni-text></uni-view></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a=""><span>0</span></uni-text></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a=""><span>0</span></uni-text></uni-view></uni-view><uni-view data-v-658fd12a="" class="header-wrapper"><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a=""><span>奶杏</span></uni-text></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a=""><span>F</span></uni-text></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a="" style="color: rgb(65, 159, 49);"><span>有货</span></uni-text></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 40%;"><uni-view data-v-658fd12a="" class="num-wrapper"><uni-text data-v-658fd12a="" class="num-button icon cuIcon-move "><span></span></uni-text><uni-text data-v-658fd12a="" class="input-num" style="width: 42px;"><span>0</span></uni-text><uni-text data-v-658fd12a="" class="num-button icon cuIcon-add "><span></span></uni-text></uni-view></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a=""><span>0</span></uni-text></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a=""><span>0</span></uni-text></uni-view></uni-view><uni-view data-v-658fd12a="" class="header-wrapper"><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a=""><span>淡紫</span></uni-text></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a=""><span>F</span></uni-text></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a="" style="color: rgb(65, 159, 49);"><span>有货</span></uni-text></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 40%;"><uni-view data-v-658fd12a="" class="num-wrapper"><uni-text data-v-658fd12a="" class="num-button icon cuIcon-move "><span></span></uni-text><uni-text data-v-658fd12a="" class="input-num" style="width: 42px;"><span>0</span></uni-text><uni-text data-v-658fd12a="" class="num-button icon cuIcon-add "><span></span></uni-text></uni-view></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a=""><span>0</span></uni-text></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a=""><span>0</span></uni-text></uni-view></uni-view><uni-view data-v-658fd12a="" class="header-wrapper"><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a=""><span>奶昔绿</span></uni-text></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a=""><span>F</span></uni-text></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a="" style="color: rgb(65, 159, 49);"><span>有货</span></uni-text></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 40%;"><uni-view data-v-658fd12a="" class="num-wrapper"><uni-text data-v-658fd12a="" class="num-button icon cuIcon-move "><span></span></uni-text><uni-text data-v-658fd12a="" class="input-num" style="width: 42px;"><span>0</span></uni-text><uni-text data-v-658fd12a="" class="num-button icon cuIcon-add "><span></span></uni-text></uni-view></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a=""><span>0</span></uni-text></uni-view><uni-view data-v-658fd12a="" class="column-item" style="width: 12%;"><uni-text data-v-658fd12a=""><span>0</span></uni-text></uni-view></uni-view></div></div></div></uni-scroll-view></uni-view><uni-view data-v-658fd12a="" class="action-wrapper"><uni-text data-v-658fd12a="" class="margin-xs"><span>数量:<uni-text data-v-658fd12a="" class="text-red text-bold"><span>3</span></uni-text></span></uni-text><uni-text data-v-658fd12a="" class="margin-xs"><span>金额:<uni-text data-v-658fd12a="" class="text-red text-bold"><span>￥21.60</span></uni-text></span></uni-text><uni-text data-v-658fd12a="" class="cu-btn bg-orange round margin-right-xs"><span>批量加购</span></uni-text><uni-text data-v-658fd12a="" class="cu-btn bg-red round margin-right-xs"><span>加入购物车</span></uni-text></uni-view></uni-view></uni-view></uni-view><uni-view data-v-4a5e5864="" data-v-658fd12a="" class="cu-modal"><uni-view data-v-4a5e5864="" class="cu-dialog"><uni-view data-v-4a5e5864="" class="cu-bar bg-white justify-end"><uni-view data-v-4a5e5864="" class="content">修改数量</uni-view><uni-view data-v-4a5e5864="" class="action"><uni-text data-v-4a5e5864="" class="cuIcon-close text-red"><span></span></uni-text></uni-view></uni-view><uni-view data-v-4a5e5864="" class="padding-sm"><uni-view data-v-4a5e5864="" class="num-wrapper2"><uni-text data-v-4a5e5864="" class="icon cuIcon-move"><span></span></uni-text><uni-input data-v-4a5e5864="" class="input-num"><div class="uni-input-wrapper"><div class="uni-input-placeholder input-placeholder" data-v-4a5e5864="" data-v-658fd12a="" data-v-025e4f16="" style="display: none;"></div><input maxlength="140" step="0.000000000000000001" enterkeyhint="done" pattern="[0-9]*" autocomplete="off" type="number" class="uni-input-input"><!----></div></uni-input><uni-text data-v-4a5e5864="" class="icon cuIcon-add"><span></span></uni-text></uni-view></uni-view><uni-view data-v-4a5e5864="" class="cu-bar bg-white justify-end"><uni-view data-v-4a5e5864="" class="action"><uni-button data-v-4a5e5864="" class="cu-btn bg-red margin-left">确定</uni-button></uni-view></uni-view></uni-view></uni-view></uni-view><uni-view data-v-025e4f16="" class="cu-modal "><uni-view data-v-025e4f16="" class="cu-dialog bg-white"><uni-view data-v-025e4f16="" class="cu-bar justify-end"><uni-view data-v-025e4f16="" class="content">提示</uni-view></uni-view><uni-view data-v-025e4f16="" class="padding-xl">抱歉，该已下架</uni-view><uni-view data-v-025e4f16="" class="padding"><uni-navigator data-v-025e4f16="" class="cu-btn margin-top response lg bg-sgreen">确定</uni-navigator></uni-view></uni-view></uni-view><uni-view data-v-b98abefa="" data-v-025e4f16=""><uni-view data-v-b98abefa="" class="cu-modal bottom-modal "><uni-view data-v-b98abefa="" class="cu-dialog"><uni-view data-v-b98abefa="" class="cu-bar bg-white"><uni-view data-v-b98abefa="" class="action text-green"></uni-view><uni-view data-v-b98abefa="" class="action text-red">取消</uni-view></uni-view><uni-view data-v-b98abefa="" class="padding flex flex-direction"><uni-view data-v-2031275d="" data-v-b98abefa="" style="display: none;"><uni-view data-v-2031275d=""><uni-button data-v-2031275d="" class="cu-btn bg-green lg round shadow-blur" style="width: 100%; display: none;">分享给微信好友</uni-button></uni-view><!----></uni-view><uni-button data-v-b98abefa="" class="cu-btn margin-tb-sm lg round shadow-blur bg-sgreen">生成海报</uni-button></uni-view></uni-view></uni-view><uni-view data-v-b98abefa="" class="cu-modal "><uni-view data-v-b98abefa="" class="cu-dialog show-bg"><uni-view data-v-b98abefa="" class="bg-white" style="height: calc(-100px + 100vh);"><img data-v-b98abefa="" src=""></uni-view><uni-view data-v-b98abefa="" class="cu-bar bg-white solid-top show-btn"><uni-view data-v-b98abefa="" class="action margin-0 flex-sub">取消</uni-view><uni-view data-v-b98abefa="" class="action margin-0 flex-sub solid-left text-red text-bold">长按图片可保存或分享</uni-view></uni-view></uni-view></uni-view><uni-view data-v-b98abefa="" id="poster"><uni-view></uni-view><uni-view data-v-15c0f2aa="" class="container" id="poster"><uni-canvas data-v-15c0f2aa="" canvas-id="canvasid" class="canvas pro"><canvas width="750" height="1800"></canvas><div style="position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; overflow: hidden;"></div><uni-resize-sensor><div><div></div></div><div><div></div></div></uni-resize-sensor></uni-canvas></uni-view></uni-view><uni-view data-v-b98abefa=""><uni-view data-v-6165a5b4="" data-v-b98abefa="" class="tki-qrcode"><uni-canvas data-v-6165a5b4="" canvas-id="tki-qrcode-canvas" class="tki-qrcode-canvas" style="width: 130px; height: 130px;"><canvas width="260" height="260"></canvas><div style="position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; overflow: hidden;"></div><uni-resize-sensor><div><div></div></div><div><div></div></div></uni-resize-sensor></uni-canvas><uni-image data-v-6165a5b4="" style="width: 130px; height: 130px; display: none;"><div style="background-image: none; background-position: 0% 0%; background-size: 100% 100%; background-repeat: no-repeat;"></div><!----></uni-image></uni-view></uni-view></uni-view><uni-view data-v-2156d5ee="" data-v-025e4f16="" class="cu-modal bottom-modal "><uni-view data-v-2156d5ee="" class="cu-dialog" style="background-color: rgb(255, 255, 255); max-height: 50vh; overflow: auto;"><uni-view data-v-2156d5ee="" class="padding-sm"><uni-view data-v-2156d5ee="" class="action-wrapper"><uni-text data-v-2156d5ee="" class="title"><span>销存记录</span></uni-text><uni-text data-v-2156d5ee="" class="cuIcon-close" style="font-size: 20px;"><span></span></uni-text></uni-view><uni-view data-v-2156d5ee="" class="header-wrapper"><uni-text data-v-2156d5ee=""><span>颜色</span></uni-text><uni-text data-v-2156d5ee=""><span>尺码</span></uni-text><uni-text data-v-2156d5ee=""><span>零售数量</span></uni-text><uni-text data-v-2156d5ee=""><span>库存数量</span></uni-text></uni-view><uni-view data-v-2156d5ee="" class="header-wrapper"><uni-text data-v-2156d5ee="" class="column-item"><span>合计</span></uni-text><uni-text data-v-2156d5ee="" class="column-item"><span>--</span></uni-text><uni-text data-v-2156d5ee="" class="column-item"><span>0</span></uni-text><uni-text data-v-2156d5ee="" class="column-item"><span>0</span></uni-text></uni-view></uni-view></uni-view></uni-view><uni-view data-v-10127225="" data-v-025e4f16="" class="back-top-wrapper" style=""><uni-text data-v-10127225="" class="cuIcon-top"><span></span></uni-text><uni-text data-v-10127225="" class="back-top-tip"><span>顶部</span></uni-text></uni-view></uni-view></uni-page-body></uni-page-wrapper></uni-page>`,
    };
  },
  methods: {
    // 清空HTML输入
    clearHtmlInput() {
      this.htmlInput = "";
      this.swiperImages = [];
      this.rtfImages = [];
    },

    // 加载测试数据
    loadTestData() {
      this.htmlInput = `<uni-page data-page="pages/goods/goods-detail/index">
        <uni-page-wrapper>
          <uni-page-body>
            <uni-view class="padding-bottom">
              <uni-view class="product-bg margin-bottom-xs">
                <uni-swiper class="screen-swiper square-dot">
                  <div class="uni-swiper-wrapper">
                    <div class="uni-swiper-slides">
                      <div class="uni-swiper-slide-frame">
                        <uni-swiper-item>
                          <uni-image>
                            <div style="background-image: url(&quot;https://oss.dsxgj.com/spu_images/621016825/1/01.jpg?x-oss-process=style/d1&quot;);"></div>
                            <img src="https://oss.dsxgj.com/spu_images/621016825/1/01.jpg?x-oss-process=style/d1" draggable="false">
                          </uni-image>
                        </uni-swiper-item>
                        <uni-swiper-item>
                          <uni-image>
                            <div style="background-image: url(&quot;https://oss.dsxgj.com/spu_images/621016825/1/02.jpg?x-oss-process=style/d1&quot;);"></div>
                            <img src="https://oss.dsxgj.com/spu_images/621016825/1/02.jpg?x-oss-process=style/d1" draggable="false">
                          </uni-image>
                        </uni-swiper-item>
                      </div>
                    </div>
                  </div>
                </uni-swiper>
              </uni-view>
              <uni-view class="bg-white">
                <uni-view style="display: inherit;">
                  <uni-view class="top">
                    <div id="rtf64">
                      <div>
                        <p>
                          <img src="https://oss.dsxgj.com/spu_images/621016825/2/1_01.jpg?x-oss-process=style/d1" style="max-width: 100%;">
                          <img src="https://oss.dsxgj.com/spu_images/621016825/2/1_02.jpg?x-oss-process=style/d1" style="max-width: 100%;">
                          <span>https://oss.dsxgj.com/spu_images/621016825/2/1_03.jpg</span>
                        </p>
                      </div>
                    </div>
                  </uni-view>
                </uni-view>
              </uni-view>
            </uni-view>
          </uni-page-body>
        </uni-page-wrapper>
      </uni-page>`;

      // 自动解析
      this.parseHtml();

      uni.showToast({
        title: "测试数据已加载",
        icon: "success",
      });
    },

    // 解析HTML代码
    parseHtml() {
      if (!this.htmlInput.trim()) {
        this.swiperImages = [];
        this.rtfImages = [];
        return;
      }

      // 解析uni-swiper-wrapper中的图片
      this.parseSwiperImages();

      // 解析rtf64中的图片
      this.parseRtfImages();
    },

    // 通用图片URL提取方法
    extractImageUrls(htmlContent) {
      const foundUrls = new Set();

      // 多种图片URL提取模式
      const imagePatterns = [
        // 1. 标准img标签src属性
        /<img[^>]+src=["']([^"']*?)(?:\?[^"']*)?["'][^>]*>/gi,
        /src=["']([^"']*?)(?:\?[^"']*)?["']/gi,

        // 2. background-image CSS样式 (各种引号格式)
        /background-image:\s*url\(["']([^"']*?)(?:\?[^"']*)?["']\)/gi,
        /background-image:\s*url\(([^)]*?)(?:\?[^)]*?)?\)/gi,

        // 3. HTML实体编码的background-image
        /background-image:\s*url\(&quot;([^&]*?)(?:\?[^&]*)?&quot;\)/gi,
        /background-image:\s*url\(&#39;([^&]*?)(?:\?[^&]*)?&#39;\)/gi,

        // 4. data-src, data-original等懒加载属性
        /data-src=["']([^"']*?)(?:\?[^"']*)?["']/gi,
        /data-original=["']([^"']*?)(?:\?[^"']*)?["']/gi,
        /data-lazy=["']([^"']*?)(?:\?[^"']*)?["']/gi,

        // 5. style属性中的background
        /style=["'][^"']*background[^"']*url\(["']?([^"')]*?)(?:\?[^"')]*)?["']?\)[^"']*["']/gi,

        // 6. 处理span标签内的图片引用
        /<span[^>]*>[^<]*https?:\/\/[^<\s]*\.(jpg|jpeg|png|gif|webp|svg)[^<\s]*[^<]*<\/span>/gi,

        // 7. 直接的URL文本匹配
        /https?:\/\/[^\s<>"']*\.(jpg|jpeg|png|gif|webp|svg)(?:\?[^\s<>"']*)?/gi,
      ];

      // 执行所有模式匹配
      for (const pattern of imagePatterns) {
        let match;
        while ((match = pattern.exec(htmlContent)) !== null) {
          let url = match[1] || match[0]; // 有些模式捕获组在index 0

          if (!url) continue;

          // 清理URL
          url = this.cleanImageUrl(url);

          if (this.isValidImageUrl(url)) {
            foundUrls.add(url);
          }
        }
      }

      return Array.from(foundUrls);
    },

    // 清理图片URL
    cleanImageUrl(url) {
      if (!url) return "";

      // 移除HTML实体编码
      url = url
        .replace(/&quot;/g, '"')
        .replace(/&amp;/g, "&")
        .replace(/&#39;/g, "'")
        .replace(/&lt;/g, "<")
        .replace(/&gt;/g, ">");

      // 移除前后空白和引号
      url = url.trim().replace(/^["']|["']$/g, "");

      // 处理协议相对URL
      if (url.startsWith("//")) {
        url = "https:" + url;
      }

      // 移除URL参数（保留原始图片地址）
      const urlParts = url.split("?");
      url = urlParts[0];

      return url;
    },

    // 验证是否为有效的图片URL
    isValidImageUrl(url) {
      if (!url || typeof url !== "string") return false;

      // 必须是HTTP/HTTPS协议
      if (!url.match(/^https?:\/\//)) return false;

      // 检查是否包含常见图片扩展名或图片服务域名
      const imageExtensions = /\.(jpg|jpeg|png|gif|webp|svg|bmp|tiff)$/i;
      const imageServices = /(oss\.|cdn\.|img\.|image\.|static\.|upload)/i;

      return (
        imageExtensions.test(url) ||
        imageServices.test(url) ||
        url.includes("image") ||
        url.includes("photo")
      );
    },

    // 解析轮播图片
    parseSwiperImages() {
      this.swiperImages = [];

      // 多种方式提取uni-swiper-wrapper中的图片
      const patterns = [
        // 匹配uni-swiper-wrapper区域
        /class="uni-swiper-wrapper"[\s\S]*?(?=<\/div>)/g,
        /uni-swiper-wrapper[\s\S]*?(?=<\/div>)/g,
        // 匹配包含swiper的更大区域
        /<uni-swiper[\s\S]*?<\/uni-swiper>/g,
        // 匹配更广泛的swiper相关区域
        /swiper[\s\S]*?(?=<\/[^>]*>)/g,
      ];

      let swiperContent = "";
      for (const pattern of patterns) {
        const matches = this.htmlInput.match(pattern);
        if (matches && matches.length > 0) {
          swiperContent = matches.join(" ");
          break;
        }
      }

      if (swiperContent) {
        // 使用通用图片提取方法
        const imageUrls = this.extractImageUrls(swiperContent);

        // 创建图片对象
        imageUrls.forEach((url) => {
          this.swiperImages.push({
            url: url,
            productCode: "",
            color: "",
          });
        });
      }
    },

    // 解析详情图片
    parseRtfImages() {
      this.rtfImages = [];

      // 多种方式提取rtf64区域的图片
      const patterns = [
        // 匹配id="rtf64"区域
        /id="rtf64"[\s\S]*?(?=<\/div>)/g,
        // 匹配更大的包含区域
        /<div[^>]*id="rtf64"[^>]*>[\s\S]*?<\/div>/g,
        // 如果没有找到rtf64，尝试查找包含大量图片的区域
        /<div[^>]*>[\s\S]*?<img[\s\S]*?<\/div>/g,
        // 查找包含rtf的区域
        /rtf[\s\S]*?(?=<\/[^>]*>)/g,
      ];

      let rtfContent = "";
      for (const pattern of patterns) {
        const matches = this.htmlInput.match(pattern);
        if (matches && matches.length > 0) {
          rtfContent = matches.join(" ");
          break;
        }
      }

      // 如果没有找到特定区域，尝试从整个HTML中提取图片
      if (!rtfContent) {
        rtfContent = this.htmlInput;
      }

      if (rtfContent) {
        // 使用通用图片提取方法
        const imageUrls = this.extractImageUrls(rtfContent);

        // 创建图片对象
        imageUrls.forEach((url) => {
          this.rtfImages.push({
            url: url,
            color: "",
          });
        });
      }
    },

    // 填充货号
    fillProductCodes() {
      if (!this.mainProductCode.trim()) {
        uni.showToast({
          title: "请输入主货号",
          icon: "none",
        });
        return;
      }

      this.swiperImages.forEach((item, index) => {
        item.productCode = `${this.mainProductCode}_${index + 1}`;
      });
    },

    // 一键填充颜色
    fillAllColors() {
      if (!this.fillColor.trim()) {
        uni.showToast({
          title: "请输入颜色",
          icon: "none",
        });
        return;
      }

      this.rtfImages.forEach((item) => {
        item.color = this.fillColor;
      });
    },

    // 复制文本
    copyText(text) {
      if (!text) {
        uni.showToast({
          title: "内容为空",
          icon: "none",
        });
        return;
      }

      // #ifdef H5
      if (navigator.clipboard) {
        navigator.clipboard
          .writeText(text)
          .then(() => {
            uni.showToast({
              title: "复制成功",
              icon: "success",
            });
          })
          .catch(() => {
            this.fallbackCopy(text);
          });
      } else {
        this.fallbackCopy(text);
      }
      // #endif

      // #ifndef H5
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: "复制成功",
            icon: "success",
          });
        },
      });
      // #endif
    },

    // 备用复制方法
    fallbackCopy(text) {
      const textArea = document.createElement("textarea");
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand("copy");
        uni.showToast({
          title: "复制成功",
          icon: "success",
        });
      } catch (err) {
        uni.showToast({
          title: "复制失败",
          icon: "none",
        });
      }
      document.body.removeChild(textArea);
    },

    // 一键复制轮播图片地址
    copyAllSwiperUrls() {
      if (this.swiperImages.length === 0) {
        uni.showToast({
          title: "没有图片地址",
          icon: "none",
        });
        return;
      }

      const urls = this.swiperImages.map((item) => item.url).join("\n");
      this.copyText(urls);
    },

    // 一键复制详情图片地址
    copyAllRtfUrls() {
      if (this.rtfImages.length === 0) {
        uni.showToast({
          title: "没有图片地址",
          icon: "none",
        });
        return;
      }

      const urls = this.rtfImages.map((item) => item.url).join("\n");
      this.copyText(urls);
    },

    // 删除轮播图片行
    deleteSwiperRow(index) {
      this.swiperImages.splice(index, 1);
    },

    // 删除详情图片行
    deleteRtfRow(index) {
      this.rtfImages.splice(index, 1);
    },

    // 清空图片地址输入
    clearImageUrls() {
      this.imageUrlsInput = "";
      this.jsonResult = "";
    },

    // 生成JSON
    generateJson() {
      if (!this.imageUrlsInput.trim()) {
        this.jsonResult = "";
        return;
      }

      const urls = this.imageUrlsInput
        .trim()
        .split("\n")
        .filter((url) => url.trim());

      if (urls.length === 0) {
        this.jsonResult = "";
        return;
      }

      const blocks = urls.map((url) => ({
        imgLink: "",
        img: {
          src: url.trim(),
          srcMobile: "",
          alt: "",
          position: "width_full",
          positionMobile: "width_full",
        },
      }));

      const result = {
        content: [
          {
            widgetName: "raShowcase",
            type: "roll",
            blocks: blocks,
          },
        ],
        version: 0.3,
      };

      this.jsonResult = JSON.stringify(result, null, 2);
    },

    // 复制JSON结果
    copyJsonResult() {
      if (!this.jsonResult) {
        uni.showToast({
          title: "JSON为空",
          icon: "none",
        });
        return;
      }

      this.copyText(this.jsonResult);
    },
  },
};
</script>

<style scoped>
.container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20px;
  color: #333;
}

.section {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.html-input {
  width: 100%;
  height: 200px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  resize: vertical;
  font-family: monospace;
}

.parse-result {
  margin-top: 12px;
  padding: 8px 12px;
  background-color: #e8f5e8;
  border: 1px solid #4caf50;
  border-radius: 4px;
}

.result-text {
  color: #2e7d32;
  font-size: 14px;
  font-weight: 500;
}

.control-row {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.input-field {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  flex: 1;
  min-width: 120px;
}

.input-field.small {
  flex: none;
  width: 100px;
}

.btn {
  padding: 8px 16px;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
}

.btn:hover {
  background-color: #0056b3;
}

.btn.small {
  padding: 6px 12px;
  font-size: 12px;
}

.clear-btn {
  padding: 6px 12px;
  background-color: #ff3b30;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.clear-btn:hover {
  background-color: #d70015;
}

.copy-all-btn {
  background-color: #34c759;
}

.copy-all-btn:hover {
  background-color: #28a745;
}

.delete-btn {
  background-color: #ff3b30;
}

.delete-btn:hover {
  background-color: #d70015;
}

.copy-btn {
  padding: 4px 8px;
  background-color: #34c759;
  color: white;
  border: none;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
  margin-left: 8px;
}

.copy-btn:hover {
  background-color: #28a745;
}

.table {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background-color: #f8f9fa;
  font-weight: bold;
  border-bottom: 1px solid #ddd;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #eee;
  align-items: center;
  min-height: 60px;
}

.table-row:last-child {
  border-bottom: none;
}

.col-image {
  flex: 0 0 100px;
  padding: 8px;
  text-align: center;
}

.col-code {
  flex: 1;
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.col-color {
  flex: 1;
  padding: 8px;
}

.col-action {
  flex: 0 0 160px;
  padding: 8px;
  display: flex;
  gap: 8px;
  justify-content: center;
}

.preview-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.image-urls-input {
  width: 100%;
  height: 150px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  resize: vertical;
  font-family: monospace;
  margin-bottom: 16px;
}

.json-result {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px;
  background-color: #f8f9fa;
  max-height: 300px;
  overflow-y: auto;
}

.json-title {
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.json-content {
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
  color: #666;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .control-row {
    flex-direction: column;
    align-items: stretch;
  }

  .input-field {
    margin-bottom: 8px;
  }

  .table-header,
  .table-row {
    flex-direction: column;
  }

  .col-image,
  .col-code,
  .col-color,
  .col-action {
    flex: none;
    width: 100%;
    border-bottom: 1px solid #eee;
  }

  .col-action {
    border-bottom: none;
  }
}
</style>
