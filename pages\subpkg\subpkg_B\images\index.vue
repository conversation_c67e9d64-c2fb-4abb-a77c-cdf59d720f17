<template>
  <view class="container">
    <view class="page-title">图片地址提取工具</view>

    <!-- HTML代码输入区域 -->
    <view class="section">
      <view class="section-title">
        HTML代码输入
        <view class="title-buttons">
          <button class="btn small" @click="loadTestData">加载测试数据</button>
          <button class="clear-btn" @click="clearHtmlInput">清空</button>
        </view>
      </view>
      <textarea
        v-model="htmlInput"
        class="html-input"
        placeholder="请输入HTML代码...&#10;&#10;支持：&#10;• 轮播图片提取 (uni-swiper-wrapper)&#10;• 详情图片提取 (rtf64)&#10;• span标签内的图片URL&#10;• background-image样式图片&#10;• 各种懒加载属性 (data-src等)&#10;&#10;无字数限制，可粘贴完整页面HTML代码"
        @input="parseHtml"
        :maxlength="-1"
      ></textarea>

      <!-- 解析结果提示 -->
      <view class="parse-result" v-if="htmlInput.trim()">
        <text class="result-text">
          解析结果: 轮播图片 {{ swiperImages.length }} 张, 详情图片
          {{ rtfImages.length }} 张
        </text>
      </view>
    </view>

    <!-- uni-swiper-wrapper 图片表格 -->
    <view class="section" v-if="swiperImages.length > 0">
      <view class="section-title">轮播图片 (uni-swiper-wrapper)</view>

      <!-- 货号填充控制 -->
      <view class="control-row">
        <input
          v-model="mainProductCode"
          class="input-field"
          placeholder="输入主货号"
        />
        <button class="btn" @click="fillProductCodes">填充货号</button>
        <button class="btn copy-all-btn" @click="copyAllSwiperUrls">
          一键复制图片地址
        </button>
      </view>

      <!-- 表格 -->
      <view class="table">
        <view class="table-header">
          <view class="col-image">图片</view>
          <view class="col-code">货号</view>
          <view class="col-color">颜色</view>
          <view class="col-action">操作</view>
        </view>
        <view
          v-for="(item, index) in swiperImages"
          :key="index"
          class="table-row"
        >
          <view class="col-image">
            <image :src="item.url" class="preview-image"></image>
          </view>
          <view class="col-code">
            <input
              v-model="item.productCode"
              class="input-field small"
              placeholder="货号"
            />
            <button class="copy-btn" @click="copyText(item.productCode)">
              复制
            </button>
          </view>
          <view class="col-color">
            <input
              v-model="item.color"
              class="input-field small"
              placeholder="颜色"
            />
          </view>
          <view class="col-action">
            <button class="btn small" @click="copyText(item.url)">
              复制地址
            </button>
            <button
              class="btn small delete-btn"
              @click="deleteSwiperRow(index)"
            >
              删除
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- rtf64 图片表格 -->
    <view class="section" v-if="rtfImages.length > 0">
      <view class="section-title">详情图片 (rtf64)</view>

      <!-- 颜色填充控制 -->
      <view class="control-row">
        <input v-model="fillColor" class="input-field" placeholder="输入颜色" />
        <button class="btn" @click="fillAllColors">一键填充颜色</button>
        <button class="btn copy-all-btn" @click="copyAllRtfUrls">
          一键复制图片地址
        </button>
      </view>

      <!-- 表格 -->
      <view class="table">
        <view class="table-header">
          <view class="col-image">图片</view>
          <view class="col-color">颜色</view>
          <view class="col-action">操作</view>
        </view>
        <view v-for="(item, index) in rtfImages" :key="index" class="table-row">
          <view class="col-image">
            <image :src="item.url" class="preview-image"></image>
          </view>
          <view class="col-color">
            <input
              v-model="item.color"
              class="input-field small"
              placeholder="颜色"
            />
            <button class="copy-btn" @click="copyText(item.color)">复制</button>
          </view>
          <view class="col-action">
            <button class="btn small" @click="copyText(item.url)">
              复制地址
            </button>
            <button class="btn small delete-btn" @click="deleteRtfRow(index)">
              删除
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- JSON生成区域 -->
    <view class="section">
      <view class="section-title">JSON生成 (blocks数组)</view>

      <view class="control-row">
        <button class="clear-btn" @click="clearImageUrls">清空</button>
        <button class="btn" @click="copyJsonResult">复制JSON</button>
      </view>

      <textarea
        v-model="imageUrlsInput"
        class="image-urls-input"
        placeholder="请输入图片地址，每行一个..."
        @input="generateJson"
      ></textarea>

      <view class="json-result">
        <text class="json-title">生成的JSON:</text>
        <text class="json-content">{{ jsonResult }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      htmlInput: "",
      swiperImages: [],
      rtfImages: [],
      mainProductCode: "",
      fillColor: "",
      imageUrlsInput: "",
      jsonResult: "",
    };
  },
  methods: {
    // 清空HTML输入
    clearHtmlInput() {
      this.htmlInput = "";
      this.swiperImages = [];
      this.rtfImages = [];
    },

    // 加载测试数据
    loadTestData() {
      this.htmlInput = `<uni-page data-page="pages/goods/goods-detail/index">
        <uni-page-wrapper>
          <uni-page-body>
            <uni-view class="padding-bottom">
              <uni-view class="product-bg margin-bottom-xs">
                <uni-swiper class="screen-swiper square-dot">
                  <div class="uni-swiper-wrapper">
                    <div class="uni-swiper-slides">
                      <div class="uni-swiper-slide-frame">
                        <uni-swiper-item>
                          <uni-image>
                            <div style="background-image: url(&quot;https://oss.dsxgj.com/spu_images/621016825/1/01.jpg?x-oss-process=style/d1&quot;);"></div>
                            <img src="https://oss.dsxgj.com/spu_images/621016825/1/01.jpg?x-oss-process=style/d1" draggable="false">
                          </uni-image>
                        </uni-swiper-item>
                        <uni-swiper-item>
                          <uni-image>
                            <div style="background-image: url(&quot;https://oss.dsxgj.com/spu_images/621016825/1/02.jpg?x-oss-process=style/d1&quot;);"></div>
                            <img src="https://oss.dsxgj.com/spu_images/621016825/1/02.jpg?x-oss-process=style/d1" draggable="false">
                          </uni-image>
                        </uni-swiper-item>
                      </div>
                    </div>
                  </div>
                </uni-swiper>
              </uni-view>
              <uni-view class="bg-white">
                <uni-view style="display: inherit;">
                  <uni-view class="top">
                    <div id="rtf64">
                      <div>
                        <p>
                          <img src="https://oss.dsxgj.com/spu_images/621016825/2/1_01.jpg?x-oss-process=style/d1" style="max-width: 100%;">
                          <img src="https://oss.dsxgj.com/spu_images/621016825/2/1_02.jpg?x-oss-process=style/d1" style="max-width: 100%;">
                          <span>https://oss.dsxgj.com/spu_images/621016825/2/1_03.jpg</span>
                        </p>
                      </div>
                    </div>
                  </uni-view>
                </uni-view>
              </uni-view>
            </uni-view>
          </uni-page-body>
        </uni-page-wrapper>
      </uni-page>`;

      // 自动解析
      this.parseHtml();

      uni.showToast({
        title: "测试数据已加载",
        icon: "success",
      });
    },

    // 解析HTML代码
    parseHtml() {
      if (!this.htmlInput.trim()) {
        this.swiperImages = [];
        this.rtfImages = [];
        return;
      }

      // 解析uni-swiper-wrapper中的图片
      this.parseSwiperImages();

      // 解析rtf64中的图片
      this.parseRtfImages();
    },

    // 通用图片URL提取方法
    extractImageUrls(htmlContent) {
      const foundUrls = new Set();

      // 多种图片URL提取模式
      const imagePatterns = [
        // 1. 标准img标签src属性
        /<img[^>]+src=["']([^"']*?)(?:\?[^"']*)?["'][^>]*>/gi,
        /src=["']([^"']*?)(?:\?[^"']*)?["']/gi,

        // 2. background-image CSS样式 (各种引号格式)
        /background-image:\s*url\(["']([^"']*?)(?:\?[^"']*)?["']\)/gi,
        /background-image:\s*url\(([^)]*?)(?:\?[^)]*?)?\)/gi,

        // 3. HTML实体编码的background-image
        /background-image:\s*url\(&quot;([^&]*?)(?:\?[^&]*)?&quot;\)/gi,
        /background-image:\s*url\(&#39;([^&]*?)(?:\?[^&]*)?&#39;\)/gi,

        // 4. data-src, data-original等懒加载属性
        /data-src=["']([^"']*?)(?:\?[^"']*)?["']/gi,
        /data-original=["']([^"']*?)(?:\?[^"']*)?["']/gi,
        /data-lazy=["']([^"']*?)(?:\?[^"']*)?["']/gi,

        // 5. style属性中的background
        /style=["'][^"']*background[^"']*url\(["']?([^"')]*?)(?:\?[^"')]*)?["']?\)[^"']*["']/gi,

        // 6. 处理span标签内的图片引用
        /<span[^>]*>[^<]*https?:\/\/[^<\s]*\.(jpg|jpeg|png|gif|webp|svg)[^<\s]*[^<]*<\/span>/gi,

        // 7. 直接的URL文本匹配
        /https?:\/\/[^\s<>"']*\.(jpg|jpeg|png|gif|webp|svg)(?:\?[^\s<>"']*)?/gi,
      ];

      // 执行所有模式匹配
      for (const pattern of imagePatterns) {
        let match;
        while ((match = pattern.exec(htmlContent)) !== null) {
          let url = match[1] || match[0]; // 有些模式捕获组在index 0

          if (!url) continue;

          // 清理URL
          url = this.cleanImageUrl(url);

          if (this.isValidImageUrl(url)) {
            foundUrls.add(url);
          }
        }
      }

      return Array.from(foundUrls);
    },

    // 清理图片URL
    cleanImageUrl(url) {
      if (!url) return "";

      // 移除HTML实体编码
      url = url
        .replace(/&quot;/g, '"')
        .replace(/&amp;/g, "&")
        .replace(/&#39;/g, "'")
        .replace(/&lt;/g, "<")
        .replace(/&gt;/g, ">");

      // 移除前后空白和引号
      url = url.trim().replace(/^["']|["']$/g, "");

      // 处理协议相对URL
      if (url.startsWith("//")) {
        url = "https:" + url;
      }

      // 移除URL参数（保留原始图片地址）
      const urlParts = url.split("?");
      url = urlParts[0];

      return url;
    },

    // 验证是否为有效的图片URL
    isValidImageUrl(url) {
      if (!url || typeof url !== "string") return false;

      // 必须是HTTP/HTTPS协议
      if (!url.match(/^https?:\/\//)) return false;

      // 检查是否包含常见图片扩展名或图片服务域名
      const imageExtensions = /\.(jpg|jpeg|png|gif|webp|svg|bmp|tiff)$/i;
      const imageServices = /(oss\.|cdn\.|img\.|image\.|static\.|upload)/i;

      return (
        imageExtensions.test(url) ||
        imageServices.test(url) ||
        url.includes("image") ||
        url.includes("photo")
      );
    },

    // 解析轮播图片
    parseSwiperImages() {
      this.swiperImages = [];

      // 多种方式提取uni-swiper-wrapper中的图片
      const patterns = [
        // 匹配uni-swiper-wrapper区域
        /class="uni-swiper-wrapper"[\s\S]*?(?=<\/div>)/g,
        /uni-swiper-wrapper[\s\S]*?(?=<\/div>)/g,
        // 匹配包含swiper的更大区域
        /<uni-swiper[\s\S]*?<\/uni-swiper>/g,
        // 匹配更广泛的swiper相关区域
        /swiper[\s\S]*?(?=<\/[^>]*>)/g,
      ];

      let swiperContent = "";
      for (const pattern of patterns) {
        const matches = this.htmlInput.match(pattern);
        if (matches && matches.length > 0) {
          swiperContent = matches.join(" ");
          break;
        }
      }

      if (swiperContent) {
        // 使用通用图片提取方法
        const imageUrls = this.extractImageUrls(swiperContent);

        // 创建图片对象
        imageUrls.forEach((url) => {
          this.swiperImages.push({
            url: url,
            productCode: "",
            color: "",
          });
        });
      }
    },

    // 解析详情图片
    parseRtfImages() {
      this.rtfImages = [];

      // 多种方式提取rtf64区域的图片
      const patterns = [
        // 匹配id="rtf64"区域
        /id="rtf64"[\s\S]*?(?=<\/div>)/g,
        // 匹配更大的包含区域
        /<div[^>]*id="rtf64"[^>]*>[\s\S]*?<\/div>/g,
        // 如果没有找到rtf64，尝试查找包含大量图片的区域
        /<div[^>]*>[\s\S]*?<img[\s\S]*?<\/div>/g,
        // 查找包含rtf的区域
        /rtf[\s\S]*?(?=<\/[^>]*>)/g,
      ];

      let rtfContent = "";
      for (const pattern of patterns) {
        const matches = this.htmlInput.match(pattern);
        if (matches && matches.length > 0) {
          rtfContent = matches.join(" ");
          break;
        }
      }

      // 如果没有找到特定区域，尝试从整个HTML中提取图片
      if (!rtfContent) {
        rtfContent = this.htmlInput;
      }

      if (rtfContent) {
        // 使用通用图片提取方法
        const imageUrls = this.extractImageUrls(rtfContent);

        // 创建图片对象
        imageUrls.forEach((url) => {
          this.rtfImages.push({
            url: url,
            color: "",
          });
        });
      }
    },

    // 填充货号
    fillProductCodes() {
      if (!this.mainProductCode.trim()) {
        uni.showToast({
          title: "请输入主货号",
          icon: "none",
        });
        return;
      }

      this.swiperImages.forEach((item, index) => {
        item.productCode = `${this.mainProductCode}_${index + 1}`;
      });
    },

    // 一键填充颜色
    fillAllColors() {
      if (!this.fillColor.trim()) {
        uni.showToast({
          title: "请输入颜色",
          icon: "none",
        });
        return;
      }

      this.rtfImages.forEach((item) => {
        item.color = this.fillColor;
      });
    },

    // 复制文本
    copyText(text) {
      if (!text) {
        uni.showToast({
          title: "内容为空",
          icon: "none",
        });
        return;
      }

      // #ifdef H5
      if (navigator.clipboard) {
        navigator.clipboard
          .writeText(text)
          .then(() => {
            uni.showToast({
              title: "复制成功",
              icon: "success",
            });
          })
          .catch(() => {
            this.fallbackCopy(text);
          });
      } else {
        this.fallbackCopy(text);
      }
      // #endif

      // #ifndef H5
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: "复制成功",
            icon: "success",
          });
        },
      });
      // #endif
    },

    // 备用复制方法
    fallbackCopy(text) {
      const textArea = document.createElement("textarea");
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand("copy");
        uni.showToast({
          title: "复制成功",
          icon: "success",
        });
      } catch (err) {
        uni.showToast({
          title: "复制失败",
          icon: "none",
        });
      }
      document.body.removeChild(textArea);
    },

    // 一键复制轮播图片地址
    copyAllSwiperUrls() {
      if (this.swiperImages.length === 0) {
        uni.showToast({
          title: "没有图片地址",
          icon: "none",
        });
        return;
      }

      const urls = this.swiperImages.map((item) => item.url).join("\n");
      this.copyText(urls);
    },

    // 一键复制详情图片地址
    copyAllRtfUrls() {
      if (this.rtfImages.length === 0) {
        uni.showToast({
          title: "没有图片地址",
          icon: "none",
        });
        return;
      }

      const urls = this.rtfImages.map((item) => item.url).join("\n");
      this.copyText(urls);
    },

    // 删除轮播图片行
    deleteSwiperRow(index) {
      this.swiperImages.splice(index, 1);
    },

    // 删除详情图片行
    deleteRtfRow(index) {
      this.rtfImages.splice(index, 1);
    },

    // 清空图片地址输入
    clearImageUrls() {
      this.imageUrlsInput = "";
      this.jsonResult = "";
    },

    // 生成JSON
    generateJson() {
      if (!this.imageUrlsInput.trim()) {
        this.jsonResult = "";
        return;
      }

      const urls = this.imageUrlsInput
        .trim()
        .split("\n")
        .filter((url) => url.trim());

      if (urls.length === 0) {
        this.jsonResult = "";
        return;
      }

      const blocks = urls.map((url) => ({
        imgLink: "",
        img: {
          src: url.trim(),
          srcMobile: "",
          alt: "",
          position: "width_full",
          positionMobile: "width_full",
        },
      }));

      const result = {
        content: [
          {
            widgetName: "raShowcase",
            type: "roll",
            blocks: blocks,
          },
        ],
        version: 0.3,
      };

      this.jsonResult = JSON.stringify(result, null, 2);
    },

    // 复制JSON结果
    copyJsonResult() {
      if (!this.jsonResult) {
        uni.showToast({
          title: "JSON为空",
          icon: "none",
        });
        return;
      }

      this.copyText(this.jsonResult);
    },
  },
};
</script>

<style scoped>
.container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20px;
  color: #333;
}

.section {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.html-input {
  width: 100%;
  min-height: 200px;
  max-height: 400px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  line-height: 1.4;
  resize: vertical;
  font-family: "Courier New", Consolas, Monaco, monospace;
  word-wrap: break-word;
  white-space: pre-wrap;
  overflow-y: auto;
  box-sizing: border-box;
}

.parse-result {
  margin-top: 12px;
  padding: 8px 12px;
  background-color: #e8f5e8;
  border: 1px solid #4caf50;
  border-radius: 4px;
}

.result-text {
  color: #2e7d32;
  font-size: 14px;
  font-weight: 500;
}

.control-row {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.input-field {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  flex: 1;
  min-width: 120px;
}

.input-field.small {
  flex: none;
  width: 100px;
}

.btn {
  padding: 8px 16px;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
}

.btn:hover {
  background-color: #0056b3;
}

.title-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.btn.small {
  padding: 6px 12px;
  font-size: 12px;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn.small:hover {
  background-color: #1976d2;
}

.clear-btn {
  padding: 6px 12px;
  background-color: #ff3b30;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.clear-btn:hover {
  background-color: #d70015;
}

.copy-all-btn {
  background-color: #34c759;
}

.copy-all-btn:hover {
  background-color: #28a745;
}

.delete-btn {
  background-color: #ff3b30;
}

.delete-btn:hover {
  background-color: #d70015;
}

.copy-btn {
  padding: 4px 8px;
  background-color: #34c759;
  color: white;
  border: none;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
  margin-left: 8px;
}

.copy-btn:hover {
  background-color: #28a745;
}

.table {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background-color: #f8f9fa;
  font-weight: bold;
  border-bottom: 1px solid #ddd;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #eee;
  align-items: center;
  min-height: 60px;
}

.table-row:last-child {
  border-bottom: none;
}

.col-image {
  flex: 0 0 100px;
  padding: 8px;
  text-align: center;
}

.col-code {
  flex: 1;
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.col-color {
  flex: 1;
  padding: 8px;
}

.col-action {
  flex: 0 0 160px;
  padding: 8px;
  display: flex;
  gap: 8px;
  justify-content: center;
}

.preview-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.image-urls-input {
  width: 100%;
  height: 150px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  resize: vertical;
  font-family: monospace;
  margin-bottom: 16px;
}

.json-result {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px;
  background-color: #f8f9fa;
  max-height: 300px;
  overflow-y: auto;
}

.json-title {
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.json-content {
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
  color: #666;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .control-row {
    flex-direction: column;
    align-items: stretch;
  }

  .input-field {
    margin-bottom: 8px;
  }

  .table-header,
  .table-row {
    flex-direction: column;
  }

  .col-image,
  .col-code,
  .col-color,
  .col-action {
    flex: none;
    width: 100%;
    border-bottom: 1px solid #eee;
  }

  .col-action {
    border-bottom: none;
  }
}
</style>
