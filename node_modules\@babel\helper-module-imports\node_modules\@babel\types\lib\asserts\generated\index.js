"use strict";

exports.__esModule = true;
exports.assertArrayExpression = assertArrayExpression;
exports.assertAssignmentExpression = assertAssignmentExpression;
exports.assertBinaryExpression = assertBinaryExpression;
exports.assertDirective = assertDirective;
exports.assertDirectiveLiteral = assertDirectiveLiteral;
exports.assertBlockStatement = assertBlockStatement;
exports.assertBreakStatement = assertBreakStatement;
exports.assertCallExpression = assertCallExpression;
exports.assertCatchClause = assertCatchClause;
exports.assertConditionalExpression = assertConditionalExpression;
exports.assertContinueStatement = assertContinueStatement;
exports.assertDebuggerStatement = assertDebuggerStatement;
exports.assertDoWhileStatement = assertDoWhileStatement;
exports.assertEmptyStatement = assertEmptyStatement;
exports.assertExpressionStatement = assertExpressionStatement;
exports.assertFile = assertFile;
exports.assertForInStatement = assertForInStatement;
exports.assertForStatement = assertForStatement;
exports.assertFunctionDeclaration = assertFunctionDeclaration;
exports.assertFunctionExpression = assertFunctionExpression;
exports.assertIdentifier = assertIdentifier;
exports.assertIfStatement = assertIfStatement;
exports.assertLabeledStatement = assertLabeledStatement;
exports.assertStringLiteral = assertStringLiteral;
exports.assertNumericLiteral = assertNumericLiteral;
exports.assertNullLiteral = assertNullLiteral;
exports.assertBooleanLiteral = assertBooleanLiteral;
exports.assertRegExpLiteral = assertRegExpLiteral;
exports.assertLogicalExpression = assertLogicalExpression;
exports.assertMemberExpression = assertMemberExpression;
exports.assertNewExpression = assertNewExpression;
exports.assertProgram = assertProgram;
exports.assertObjectExpression = assertObjectExpression;
exports.assertObjectMethod = assertObjectMethod;
exports.assertObjectProperty = assertObjectProperty;
exports.assertRestElement = assertRestElement;
exports.assertReturnStatement = assertReturnStatement;
exports.assertSequenceExpression = assertSequenceExpression;
exports.assertSwitchCase = assertSwitchCase;
exports.assertSwitchStatement = assertSwitchStatement;
exports.assertThisExpression = assertThisExpression;
exports.assertThrowStatement = assertThrowStatement;
exports.assertTryStatement = assertTryStatement;
exports.assertUnaryExpression = assertUnaryExpression;
exports.assertUpdateExpression = assertUpdateExpression;
exports.assertVariableDeclaration = assertVariableDeclaration;
exports.assertVariableDeclarator = assertVariableDeclarator;
exports.assertWhileStatement = assertWhileStatement;
exports.assertWithStatement = assertWithStatement;
exports.assertAssignmentPattern = assertAssignmentPattern;
exports.assertArrayPattern = assertArrayPattern;
exports.assertArrowFunctionExpression = assertArrowFunctionExpression;
exports.assertClassBody = assertClassBody;
exports.assertClassDeclaration = assertClassDeclaration;
exports.assertClassExpression = assertClassExpression;
exports.assertExportAllDeclaration = assertExportAllDeclaration;
exports.assertExportDefaultDeclaration = assertExportDefaultDeclaration;
exports.assertExportNamedDeclaration = assertExportNamedDeclaration;
exports.assertExportSpecifier = assertExportSpecifier;
exports.assertForOfStatement = assertForOfStatement;
exports.assertImportDeclaration = assertImportDeclaration;
exports.assertImportDefaultSpecifier = assertImportDefaultSpecifier;
exports.assertImportNamespaceSpecifier = assertImportNamespaceSpecifier;
exports.assertImportSpecifier = assertImportSpecifier;
exports.assertMetaProperty = assertMetaProperty;
exports.assertClassMethod = assertClassMethod;
exports.assertObjectPattern = assertObjectPattern;
exports.assertSpreadElement = assertSpreadElement;
exports.assertSuper = assertSuper;
exports.assertTaggedTemplateExpression = assertTaggedTemplateExpression;
exports.assertTemplateElement = assertTemplateElement;
exports.assertTemplateLiteral = assertTemplateLiteral;
exports.assertYieldExpression = assertYieldExpression;
exports.assertAnyTypeAnnotation = assertAnyTypeAnnotation;
exports.assertArrayTypeAnnotation = assertArrayTypeAnnotation;
exports.assertBooleanTypeAnnotation = assertBooleanTypeAnnotation;
exports.assertBooleanLiteralTypeAnnotation = assertBooleanLiteralTypeAnnotation;
exports.assertNullLiteralTypeAnnotation = assertNullLiteralTypeAnnotation;
exports.assertClassImplements = assertClassImplements;
exports.assertDeclareClass = assertDeclareClass;
exports.assertDeclareFunction = assertDeclareFunction;
exports.assertDeclareInterface = assertDeclareInterface;
exports.assertDeclareModule = assertDeclareModule;
exports.assertDeclareModuleExports = assertDeclareModuleExports;
exports.assertDeclareTypeAlias = assertDeclareTypeAlias;
exports.assertDeclareOpaqueType = assertDeclareOpaqueType;
exports.assertDeclareVariable = assertDeclareVariable;
exports.assertDeclareExportDeclaration = assertDeclareExportDeclaration;
exports.assertDeclareExportAllDeclaration = assertDeclareExportAllDeclaration;
exports.assertDeclaredPredicate = assertDeclaredPredicate;
exports.assertExistsTypeAnnotation = assertExistsTypeAnnotation;
exports.assertFunctionTypeAnnotation = assertFunctionTypeAnnotation;
exports.assertFunctionTypeParam = assertFunctionTypeParam;
exports.assertGenericTypeAnnotation = assertGenericTypeAnnotation;
exports.assertInferredPredicate = assertInferredPredicate;
exports.assertInterfaceExtends = assertInterfaceExtends;
exports.assertInterfaceDeclaration = assertInterfaceDeclaration;
exports.assertIntersectionTypeAnnotation = assertIntersectionTypeAnnotation;
exports.assertMixedTypeAnnotation = assertMixedTypeAnnotation;
exports.assertEmptyTypeAnnotation = assertEmptyTypeAnnotation;
exports.assertNullableTypeAnnotation = assertNullableTypeAnnotation;
exports.assertNumberLiteralTypeAnnotation = assertNumberLiteralTypeAnnotation;
exports.assertNumberTypeAnnotation = assertNumberTypeAnnotation;
exports.assertObjectTypeAnnotation = assertObjectTypeAnnotation;
exports.assertObjectTypeCallProperty = assertObjectTypeCallProperty;
exports.assertObjectTypeIndexer = assertObjectTypeIndexer;
exports.assertObjectTypeProperty = assertObjectTypeProperty;
exports.assertObjectTypeSpreadProperty = assertObjectTypeSpreadProperty;
exports.assertOpaqueType = assertOpaqueType;
exports.assertQualifiedTypeIdentifier = assertQualifiedTypeIdentifier;
exports.assertStringLiteralTypeAnnotation = assertStringLiteralTypeAnnotation;
exports.assertStringTypeAnnotation = assertStringTypeAnnotation;
exports.assertThisTypeAnnotation = assertThisTypeAnnotation;
exports.assertTupleTypeAnnotation = assertTupleTypeAnnotation;
exports.assertTypeofTypeAnnotation = assertTypeofTypeAnnotation;
exports.assertTypeAlias = assertTypeAlias;
exports.assertTypeAnnotation = assertTypeAnnotation;
exports.assertTypeCastExpression = assertTypeCastExpression;
exports.assertTypeParameter = assertTypeParameter;
exports.assertTypeParameterDeclaration = assertTypeParameterDeclaration;
exports.assertTypeParameterInstantiation = assertTypeParameterInstantiation;
exports.assertUnionTypeAnnotation = assertUnionTypeAnnotation;
exports.assertVoidTypeAnnotation = assertVoidTypeAnnotation;
exports.assertJSXAttribute = assertJSXAttribute;
exports.assertJSXClosingElement = assertJSXClosingElement;
exports.assertJSXElement = assertJSXElement;
exports.assertJSXEmptyExpression = assertJSXEmptyExpression;
exports.assertJSXExpressionContainer = assertJSXExpressionContainer;
exports.assertJSXSpreadChild = assertJSXSpreadChild;
exports.assertJSXIdentifier = assertJSXIdentifier;
exports.assertJSXMemberExpression = assertJSXMemberExpression;
exports.assertJSXNamespacedName = assertJSXNamespacedName;
exports.assertJSXOpeningElement = assertJSXOpeningElement;
exports.assertJSXSpreadAttribute = assertJSXSpreadAttribute;
exports.assertJSXText = assertJSXText;
exports.assertJSXFragment = assertJSXFragment;
exports.assertJSXOpeningFragment = assertJSXOpeningFragment;
exports.assertJSXClosingFragment = assertJSXClosingFragment;
exports.assertNoop = assertNoop;
exports.assertParenthesizedExpression = assertParenthesizedExpression;
exports.assertAwaitExpression = assertAwaitExpression;
exports.assertBindExpression = assertBindExpression;
exports.assertClassProperty = assertClassProperty;
exports.assertImport = assertImport;
exports.assertDecorator = assertDecorator;
exports.assertDoExpression = assertDoExpression;
exports.assertExportDefaultSpecifier = assertExportDefaultSpecifier;
exports.assertExportNamespaceSpecifier = assertExportNamespaceSpecifier;
exports.assertTSParameterProperty = assertTSParameterProperty;
exports.assertTSDeclareFunction = assertTSDeclareFunction;
exports.assertTSDeclareMethod = assertTSDeclareMethod;
exports.assertTSQualifiedName = assertTSQualifiedName;
exports.assertTSCallSignatureDeclaration = assertTSCallSignatureDeclaration;
exports.assertTSConstructSignatureDeclaration = assertTSConstructSignatureDeclaration;
exports.assertTSPropertySignature = assertTSPropertySignature;
exports.assertTSMethodSignature = assertTSMethodSignature;
exports.assertTSIndexSignature = assertTSIndexSignature;
exports.assertTSAnyKeyword = assertTSAnyKeyword;
exports.assertTSNumberKeyword = assertTSNumberKeyword;
exports.assertTSObjectKeyword = assertTSObjectKeyword;
exports.assertTSBooleanKeyword = assertTSBooleanKeyword;
exports.assertTSStringKeyword = assertTSStringKeyword;
exports.assertTSSymbolKeyword = assertTSSymbolKeyword;
exports.assertTSVoidKeyword = assertTSVoidKeyword;
exports.assertTSUndefinedKeyword = assertTSUndefinedKeyword;
exports.assertTSNullKeyword = assertTSNullKeyword;
exports.assertTSNeverKeyword = assertTSNeverKeyword;
exports.assertTSThisType = assertTSThisType;
exports.assertTSFunctionType = assertTSFunctionType;
exports.assertTSConstructorType = assertTSConstructorType;
exports.assertTSTypeReference = assertTSTypeReference;
exports.assertTSTypePredicate = assertTSTypePredicate;
exports.assertTSTypeQuery = assertTSTypeQuery;
exports.assertTSTypeLiteral = assertTSTypeLiteral;
exports.assertTSArrayType = assertTSArrayType;
exports.assertTSTupleType = assertTSTupleType;
exports.assertTSUnionType = assertTSUnionType;
exports.assertTSIntersectionType = assertTSIntersectionType;
exports.assertTSParenthesizedType = assertTSParenthesizedType;
exports.assertTSTypeOperator = assertTSTypeOperator;
exports.assertTSIndexedAccessType = assertTSIndexedAccessType;
exports.assertTSMappedType = assertTSMappedType;
exports.assertTSLiteralType = assertTSLiteralType;
exports.assertTSExpressionWithTypeArguments = assertTSExpressionWithTypeArguments;
exports.assertTSInterfaceDeclaration = assertTSInterfaceDeclaration;
exports.assertTSInterfaceBody = assertTSInterfaceBody;
exports.assertTSTypeAliasDeclaration = assertTSTypeAliasDeclaration;
exports.assertTSAsExpression = assertTSAsExpression;
exports.assertTSTypeAssertion = assertTSTypeAssertion;
exports.assertTSEnumDeclaration = assertTSEnumDeclaration;
exports.assertTSEnumMember = assertTSEnumMember;
exports.assertTSModuleDeclaration = assertTSModuleDeclaration;
exports.assertTSModuleBlock = assertTSModuleBlock;
exports.assertTSImportEqualsDeclaration = assertTSImportEqualsDeclaration;
exports.assertTSExternalModuleReference = assertTSExternalModuleReference;
exports.assertTSNonNullExpression = assertTSNonNullExpression;
exports.assertTSExportAssignment = assertTSExportAssignment;
exports.assertTSNamespaceExportDeclaration = assertTSNamespaceExportDeclaration;
exports.assertTSTypeAnnotation = assertTSTypeAnnotation;
exports.assertTSTypeParameterInstantiation = assertTSTypeParameterInstantiation;
exports.assertTSTypeParameterDeclaration = assertTSTypeParameterDeclaration;
exports.assertTSTypeParameter = assertTSTypeParameter;
exports.assertExpression = assertExpression;
exports.assertBinary = assertBinary;
exports.assertScopable = assertScopable;
exports.assertBlockParent = assertBlockParent;
exports.assertBlock = assertBlock;
exports.assertStatement = assertStatement;
exports.assertTerminatorless = assertTerminatorless;
exports.assertCompletionStatement = assertCompletionStatement;
exports.assertConditional = assertConditional;
exports.assertLoop = assertLoop;
exports.assertWhile = assertWhile;
exports.assertExpressionWrapper = assertExpressionWrapper;
exports.assertFor = assertFor;
exports.assertForXStatement = assertForXStatement;
exports.assertFunction = assertFunction;
exports.assertFunctionParent = assertFunctionParent;
exports.assertPureish = assertPureish;
exports.assertDeclaration = assertDeclaration;
exports.assertPatternLike = assertPatternLike;
exports.assertLVal = assertLVal;
exports.assertTSEntityName = assertTSEntityName;
exports.assertLiteral = assertLiteral;
exports.assertImmutable = assertImmutable;
exports.assertUserWhitespacable = assertUserWhitespacable;
exports.assertMethod = assertMethod;
exports.assertObjectMember = assertObjectMember;
exports.assertProperty = assertProperty;
exports.assertUnaryLike = assertUnaryLike;
exports.assertPattern = assertPattern;
exports.assertClass = assertClass;
exports.assertModuleDeclaration = assertModuleDeclaration;
exports.assertExportDeclaration = assertExportDeclaration;
exports.assertModuleSpecifier = assertModuleSpecifier;
exports.assertFlow = assertFlow;
exports.assertFlowBaseAnnotation = assertFlowBaseAnnotation;
exports.assertFlowDeclaration = assertFlowDeclaration;
exports.assertFlowPredicate = assertFlowPredicate;
exports.assertJSX = assertJSX;
exports.assertTSTypeElement = assertTSTypeElement;
exports.assertTSType = assertTSType;
exports.assertNumberLiteral = assertNumberLiteral;
exports.assertRegexLiteral = assertRegexLiteral;
exports.assertRestProperty = assertRestProperty;
exports.assertSpreadProperty = assertSpreadProperty;

var _is = _interopRequireDefault(require("../../validators/is"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function assert(type, node, opts) {
  if (!(0, _is.default)(type, node, opts)) {
    throw new Error("Expected type \"" + type + "\" with option " + JSON.stringify(opts) + ", but instead got \"" + node.type + "\".");
  }
}

function assertArrayExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ArrayExpression", node, opts);
}

function assertAssignmentExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("AssignmentExpression", node, opts);
}

function assertBinaryExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("BinaryExpression", node, opts);
}

function assertDirective(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Directive", node, opts);
}

function assertDirectiveLiteral(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("DirectiveLiteral", node, opts);
}

function assertBlockStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("BlockStatement", node, opts);
}

function assertBreakStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("BreakStatement", node, opts);
}

function assertCallExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("CallExpression", node, opts);
}

function assertCatchClause(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("CatchClause", node, opts);
}

function assertConditionalExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ConditionalExpression", node, opts);
}

function assertContinueStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ContinueStatement", node, opts);
}

function assertDebuggerStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("DebuggerStatement", node, opts);
}

function assertDoWhileStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("DoWhileStatement", node, opts);
}

function assertEmptyStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("EmptyStatement", node, opts);
}

function assertExpressionStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ExpressionStatement", node, opts);
}

function assertFile(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("File", node, opts);
}

function assertForInStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ForInStatement", node, opts);
}

function assertForStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ForStatement", node, opts);
}

function assertFunctionDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("FunctionDeclaration", node, opts);
}

function assertFunctionExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("FunctionExpression", node, opts);
}

function assertIdentifier(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Identifier", node, opts);
}

function assertIfStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("IfStatement", node, opts);
}

function assertLabeledStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("LabeledStatement", node, opts);
}

function assertStringLiteral(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("StringLiteral", node, opts);
}

function assertNumericLiteral(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("NumericLiteral", node, opts);
}

function assertNullLiteral(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("NullLiteral", node, opts);
}

function assertBooleanLiteral(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("BooleanLiteral", node, opts);
}

function assertRegExpLiteral(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("RegExpLiteral", node, opts);
}

function assertLogicalExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("LogicalExpression", node, opts);
}

function assertMemberExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("MemberExpression", node, opts);
}

function assertNewExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("NewExpression", node, opts);
}

function assertProgram(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Program", node, opts);
}

function assertObjectExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ObjectExpression", node, opts);
}

function assertObjectMethod(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ObjectMethod", node, opts);
}

function assertObjectProperty(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ObjectProperty", node, opts);
}

function assertRestElement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("RestElement", node, opts);
}

function assertReturnStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ReturnStatement", node, opts);
}

function assertSequenceExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("SequenceExpression", node, opts);
}

function assertSwitchCase(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("SwitchCase", node, opts);
}

function assertSwitchStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("SwitchStatement", node, opts);
}

function assertThisExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ThisExpression", node, opts);
}

function assertThrowStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ThrowStatement", node, opts);
}

function assertTryStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TryStatement", node, opts);
}

function assertUnaryExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("UnaryExpression", node, opts);
}

function assertUpdateExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("UpdateExpression", node, opts);
}

function assertVariableDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("VariableDeclaration", node, opts);
}

function assertVariableDeclarator(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("VariableDeclarator", node, opts);
}

function assertWhileStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("WhileStatement", node, opts);
}

function assertWithStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("WithStatement", node, opts);
}

function assertAssignmentPattern(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("AssignmentPattern", node, opts);
}

function assertArrayPattern(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ArrayPattern", node, opts);
}

function assertArrowFunctionExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ArrowFunctionExpression", node, opts);
}

function assertClassBody(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ClassBody", node, opts);
}

function assertClassDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ClassDeclaration", node, opts);
}

function assertClassExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ClassExpression", node, opts);
}

function assertExportAllDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ExportAllDeclaration", node, opts);
}

function assertExportDefaultDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ExportDefaultDeclaration", node, opts);
}

function assertExportNamedDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ExportNamedDeclaration", node, opts);
}

function assertExportSpecifier(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ExportSpecifier", node, opts);
}

function assertForOfStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ForOfStatement", node, opts);
}

function assertImportDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ImportDeclaration", node, opts);
}

function assertImportDefaultSpecifier(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ImportDefaultSpecifier", node, opts);
}

function assertImportNamespaceSpecifier(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ImportNamespaceSpecifier", node, opts);
}

function assertImportSpecifier(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ImportSpecifier", node, opts);
}

function assertMetaProperty(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("MetaProperty", node, opts);
}

function assertClassMethod(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ClassMethod", node, opts);
}

function assertObjectPattern(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ObjectPattern", node, opts);
}

function assertSpreadElement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("SpreadElement", node, opts);
}

function assertSuper(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Super", node, opts);
}

function assertTaggedTemplateExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TaggedTemplateExpression", node, opts);
}

function assertTemplateElement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TemplateElement", node, opts);
}

function assertTemplateLiteral(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TemplateLiteral", node, opts);
}

function assertYieldExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("YieldExpression", node, opts);
}

function assertAnyTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("AnyTypeAnnotation", node, opts);
}

function assertArrayTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ArrayTypeAnnotation", node, opts);
}

function assertBooleanTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("BooleanTypeAnnotation", node, opts);
}

function assertBooleanLiteralTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("BooleanLiteralTypeAnnotation", node, opts);
}

function assertNullLiteralTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("NullLiteralTypeAnnotation", node, opts);
}

function assertClassImplements(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ClassImplements", node, opts);
}

function assertDeclareClass(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("DeclareClass", node, opts);
}

function assertDeclareFunction(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("DeclareFunction", node, opts);
}

function assertDeclareInterface(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("DeclareInterface", node, opts);
}

function assertDeclareModule(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("DeclareModule", node, opts);
}

function assertDeclareModuleExports(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("DeclareModuleExports", node, opts);
}

function assertDeclareTypeAlias(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("DeclareTypeAlias", node, opts);
}

function assertDeclareOpaqueType(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("DeclareOpaqueType", node, opts);
}

function assertDeclareVariable(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("DeclareVariable", node, opts);
}

function assertDeclareExportDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("DeclareExportDeclaration", node, opts);
}

function assertDeclareExportAllDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("DeclareExportAllDeclaration", node, opts);
}

function assertDeclaredPredicate(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("DeclaredPredicate", node, opts);
}

function assertExistsTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ExistsTypeAnnotation", node, opts);
}

function assertFunctionTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("FunctionTypeAnnotation", node, opts);
}

function assertFunctionTypeParam(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("FunctionTypeParam", node, opts);
}

function assertGenericTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("GenericTypeAnnotation", node, opts);
}

function assertInferredPredicate(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("InferredPredicate", node, opts);
}

function assertInterfaceExtends(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("InterfaceExtends", node, opts);
}

function assertInterfaceDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("InterfaceDeclaration", node, opts);
}

function assertIntersectionTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("IntersectionTypeAnnotation", node, opts);
}

function assertMixedTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("MixedTypeAnnotation", node, opts);
}

function assertEmptyTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("EmptyTypeAnnotation", node, opts);
}

function assertNullableTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("NullableTypeAnnotation", node, opts);
}

function assertNumberLiteralTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("NumberLiteralTypeAnnotation", node, opts);
}

function assertNumberTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("NumberTypeAnnotation", node, opts);
}

function assertObjectTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ObjectTypeAnnotation", node, opts);
}

function assertObjectTypeCallProperty(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ObjectTypeCallProperty", node, opts);
}

function assertObjectTypeIndexer(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ObjectTypeIndexer", node, opts);
}

function assertObjectTypeProperty(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ObjectTypeProperty", node, opts);
}

function assertObjectTypeSpreadProperty(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ObjectTypeSpreadProperty", node, opts);
}

function assertOpaqueType(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("OpaqueType", node, opts);
}

function assertQualifiedTypeIdentifier(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("QualifiedTypeIdentifier", node, opts);
}

function assertStringLiteralTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("StringLiteralTypeAnnotation", node, opts);
}

function assertStringTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("StringTypeAnnotation", node, opts);
}

function assertThisTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ThisTypeAnnotation", node, opts);
}

function assertTupleTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TupleTypeAnnotation", node, opts);
}

function assertTypeofTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TypeofTypeAnnotation", node, opts);
}

function assertTypeAlias(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TypeAlias", node, opts);
}

function assertTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TypeAnnotation", node, opts);
}

function assertTypeCastExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TypeCastExpression", node, opts);
}

function assertTypeParameter(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TypeParameter", node, opts);
}

function assertTypeParameterDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TypeParameterDeclaration", node, opts);
}

function assertTypeParameterInstantiation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TypeParameterInstantiation", node, opts);
}

function assertUnionTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("UnionTypeAnnotation", node, opts);
}

function assertVoidTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("VoidTypeAnnotation", node, opts);
}

function assertJSXAttribute(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("JSXAttribute", node, opts);
}

function assertJSXClosingElement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("JSXClosingElement", node, opts);
}

function assertJSXElement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("JSXElement", node, opts);
}

function assertJSXEmptyExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("JSXEmptyExpression", node, opts);
}

function assertJSXExpressionContainer(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("JSXExpressionContainer", node, opts);
}

function assertJSXSpreadChild(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("JSXSpreadChild", node, opts);
}

function assertJSXIdentifier(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("JSXIdentifier", node, opts);
}

function assertJSXMemberExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("JSXMemberExpression", node, opts);
}

function assertJSXNamespacedName(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("JSXNamespacedName", node, opts);
}

function assertJSXOpeningElement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("JSXOpeningElement", node, opts);
}

function assertJSXSpreadAttribute(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("JSXSpreadAttribute", node, opts);
}

function assertJSXText(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("JSXText", node, opts);
}

function assertJSXFragment(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("JSXFragment", node, opts);
}

function assertJSXOpeningFragment(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("JSXOpeningFragment", node, opts);
}

function assertJSXClosingFragment(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("JSXClosingFragment", node, opts);
}

function assertNoop(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Noop", node, opts);
}

function assertParenthesizedExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ParenthesizedExpression", node, opts);
}

function assertAwaitExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("AwaitExpression", node, opts);
}

function assertBindExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("BindExpression", node, opts);
}

function assertClassProperty(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ClassProperty", node, opts);
}

function assertImport(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Import", node, opts);
}

function assertDecorator(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Decorator", node, opts);
}

function assertDoExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("DoExpression", node, opts);
}

function assertExportDefaultSpecifier(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ExportDefaultSpecifier", node, opts);
}

function assertExportNamespaceSpecifier(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ExportNamespaceSpecifier", node, opts);
}

function assertTSParameterProperty(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSParameterProperty", node, opts);
}

function assertTSDeclareFunction(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSDeclareFunction", node, opts);
}

function assertTSDeclareMethod(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSDeclareMethod", node, opts);
}

function assertTSQualifiedName(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSQualifiedName", node, opts);
}

function assertTSCallSignatureDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSCallSignatureDeclaration", node, opts);
}

function assertTSConstructSignatureDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSConstructSignatureDeclaration", node, opts);
}

function assertTSPropertySignature(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSPropertySignature", node, opts);
}

function assertTSMethodSignature(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSMethodSignature", node, opts);
}

function assertTSIndexSignature(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSIndexSignature", node, opts);
}

function assertTSAnyKeyword(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSAnyKeyword", node, opts);
}

function assertTSNumberKeyword(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSNumberKeyword", node, opts);
}

function assertTSObjectKeyword(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSObjectKeyword", node, opts);
}

function assertTSBooleanKeyword(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSBooleanKeyword", node, opts);
}

function assertTSStringKeyword(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSStringKeyword", node, opts);
}

function assertTSSymbolKeyword(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSSymbolKeyword", node, opts);
}

function assertTSVoidKeyword(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSVoidKeyword", node, opts);
}

function assertTSUndefinedKeyword(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSUndefinedKeyword", node, opts);
}

function assertTSNullKeyword(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSNullKeyword", node, opts);
}

function assertTSNeverKeyword(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSNeverKeyword", node, opts);
}

function assertTSThisType(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSThisType", node, opts);
}

function assertTSFunctionType(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSFunctionType", node, opts);
}

function assertTSConstructorType(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSConstructorType", node, opts);
}

function assertTSTypeReference(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSTypeReference", node, opts);
}

function assertTSTypePredicate(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSTypePredicate", node, opts);
}

function assertTSTypeQuery(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSTypeQuery", node, opts);
}

function assertTSTypeLiteral(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSTypeLiteral", node, opts);
}

function assertTSArrayType(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSArrayType", node, opts);
}

function assertTSTupleType(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSTupleType", node, opts);
}

function assertTSUnionType(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSUnionType", node, opts);
}

function assertTSIntersectionType(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSIntersectionType", node, opts);
}

function assertTSParenthesizedType(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSParenthesizedType", node, opts);
}

function assertTSTypeOperator(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSTypeOperator", node, opts);
}

function assertTSIndexedAccessType(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSIndexedAccessType", node, opts);
}

function assertTSMappedType(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSMappedType", node, opts);
}

function assertTSLiteralType(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSLiteralType", node, opts);
}

function assertTSExpressionWithTypeArguments(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSExpressionWithTypeArguments", node, opts);
}

function assertTSInterfaceDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSInterfaceDeclaration", node, opts);
}

function assertTSInterfaceBody(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSInterfaceBody", node, opts);
}

function assertTSTypeAliasDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSTypeAliasDeclaration", node, opts);
}

function assertTSAsExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSAsExpression", node, opts);
}

function assertTSTypeAssertion(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSTypeAssertion", node, opts);
}

function assertTSEnumDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSEnumDeclaration", node, opts);
}

function assertTSEnumMember(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSEnumMember", node, opts);
}

function assertTSModuleDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSModuleDeclaration", node, opts);
}

function assertTSModuleBlock(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSModuleBlock", node, opts);
}

function assertTSImportEqualsDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSImportEqualsDeclaration", node, opts);
}

function assertTSExternalModuleReference(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSExternalModuleReference", node, opts);
}

function assertTSNonNullExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSNonNullExpression", node, opts);
}

function assertTSExportAssignment(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSExportAssignment", node, opts);
}

function assertTSNamespaceExportDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSNamespaceExportDeclaration", node, opts);
}

function assertTSTypeAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSTypeAnnotation", node, opts);
}

function assertTSTypeParameterInstantiation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSTypeParameterInstantiation", node, opts);
}

function assertTSTypeParameterDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSTypeParameterDeclaration", node, opts);
}

function assertTSTypeParameter(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSTypeParameter", node, opts);
}

function assertExpression(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Expression", node, opts);
}

function assertBinary(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Binary", node, opts);
}

function assertScopable(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Scopable", node, opts);
}

function assertBlockParent(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("BlockParent", node, opts);
}

function assertBlock(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Block", node, opts);
}

function assertStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Statement", node, opts);
}

function assertTerminatorless(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Terminatorless", node, opts);
}

function assertCompletionStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("CompletionStatement", node, opts);
}

function assertConditional(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Conditional", node, opts);
}

function assertLoop(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Loop", node, opts);
}

function assertWhile(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("While", node, opts);
}

function assertExpressionWrapper(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ExpressionWrapper", node, opts);
}

function assertFor(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("For", node, opts);
}

function assertForXStatement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ForXStatement", node, opts);
}

function assertFunction(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Function", node, opts);
}

function assertFunctionParent(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("FunctionParent", node, opts);
}

function assertPureish(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Pureish", node, opts);
}

function assertDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Declaration", node, opts);
}

function assertPatternLike(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("PatternLike", node, opts);
}

function assertLVal(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("LVal", node, opts);
}

function assertTSEntityName(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSEntityName", node, opts);
}

function assertLiteral(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Literal", node, opts);
}

function assertImmutable(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Immutable", node, opts);
}

function assertUserWhitespacable(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("UserWhitespacable", node, opts);
}

function assertMethod(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Method", node, opts);
}

function assertObjectMember(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ObjectMember", node, opts);
}

function assertProperty(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Property", node, opts);
}

function assertUnaryLike(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("UnaryLike", node, opts);
}

function assertPattern(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Pattern", node, opts);
}

function assertClass(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Class", node, opts);
}

function assertModuleDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ModuleDeclaration", node, opts);
}

function assertExportDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ExportDeclaration", node, opts);
}

function assertModuleSpecifier(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("ModuleSpecifier", node, opts);
}

function assertFlow(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("Flow", node, opts);
}

function assertFlowBaseAnnotation(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("FlowBaseAnnotation", node, opts);
}

function assertFlowDeclaration(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("FlowDeclaration", node, opts);
}

function assertFlowPredicate(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("FlowPredicate", node, opts);
}

function assertJSX(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("JSX", node, opts);
}

function assertTSTypeElement(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSTypeElement", node, opts);
}

function assertTSType(node, opts) {
  if (opts === void 0) {
    opts = {};
  }

  assert("TSType", node, opts);
}

function assertNumberLiteral(node, opts) {
  console.trace("The node type NumberLiteral has been renamed to NumericLiteral");
  assert("NumberLiteral", node, opts);
}

function assertRegexLiteral(node, opts) {
  console.trace("The node type RegexLiteral has been renamed to RegExpLiteral");
  assert("RegexLiteral", node, opts);
}

function assertRestProperty(node, opts) {
  console.trace("The node type RestProperty has been renamed to RestElement");
  assert("RestProperty", node, opts);
}

function assertSpreadProperty(node, opts) {
  console.trace("The node type SpreadProperty has been renamed to SpreadElement");
  assert("SpreadProperty", node, opts);
}