import request, {
	Method
} from "@/utils/request";
/**
 * 下载标签
 * @returns {AxiosPromise}
 */
export function getByShopId(params) {
	return request({
		url: "/v2/posting/fbs/package-label",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}

/**
 * 获取订单
 * @returns {AxiosPromise}
 */
export function getOrder(params) {
	return request({
		url: "/v3/posting/fbs/unfulfilled/list",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}

/**
 * 获取商品详情
 * @returns {AxiosPromise}
 */
export function getGoodsDetail(params) {
	return request({
		url: "/v1/product/info/description",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}

/**
 * 获取商品列表
 * @returns {AxiosPromise}
 */
export function getGoodsList(params) {
	return request({
		url: "/v3/product/list",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}

/**
 * 获取商品图片
 * @returns {AxiosPromise}
 */
export function getGoodsImg(params) {
	return request({
		url: "/v2/product/pictures/info",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}

/**
 * 通过SKU创建商品
 * @returns {AxiosPromise}
 */
export function createProductBySKu(params) {
	return request({
		url: "/v1/product/import-by-sku",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}

/**
 * 根据标识符获取商品信息
 * @returns {AxiosPromise}
 */
export function getGoodsInfo(params) {
	return request({
		url: "/v3/product/info/list",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}

/**
 * 创建或更新商品
 * @returns {AxiosPromise}
 */
export function createProduct(params) {
	return request({
		url: "/v3/product/import",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}

/**
 * 从卖家的系统中改变商品货号
 * @returns {AxiosPromise}
 */
export function updateOfferId(params) {
	return request({
		url: "/v1/product/update/offer-id",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}