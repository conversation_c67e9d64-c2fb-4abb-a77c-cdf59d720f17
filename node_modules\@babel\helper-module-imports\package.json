{"name": "@babel/helper-module-imports", "version": "7.0.0-beta.35", "description": "Babel helper functions for inserting module loads", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "main": "lib/index.js", "dependencies": {"@babel/types": "7.0.0-beta.35", "lodash": "^4.2.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.35"}}