import request, {
	Method
} from "@/utils/request";

/**
 * 活动清单
 * @returns {AxiosPromise}
 */
export function getActions() {
	return request({
		url: "/v1/actions",
		config: {
			method: Method.GET,
			loading: true,
			needToken: true
		},
	});
}

/**
 * 可用的促销商品清单
 * @returns {AxiosPromise}
 */
export function getCandidates(params) {
	return request({
		url: "/v1/actions/candidates",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}

/**
 * 参与 活动的商品列表
 * @returns {AxiosPromise}
 */
export function getActionsList(params) {
	return request({
		url: "/v1/actions/products",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}

/**
 * 在促销活动中增加一个商品
 * @returns {AxiosPromise}
 */
export function addActionsProducts(params) {
	return request({
		url: "/v1/actions/products/activate",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}

/**
 * 从活动中删除商品
 * @returns {AxiosPromise}
 */
export function delActionsProducts(params) {
	return request({
		url: "/v1/actions/products/deactivate",
		config: {
			method: Method.POST,
			loading: true,
			needToken: true
		},
		params,
	});
}